#!/usr/bin/env python3
"""
Report Generator Module
Generates comprehensive reports from network discovery data
"""

import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
from tabulate import tabulate
import os
import concurrent.futures


def generate_comprehensive_report(report_data: Dict[str, Any]) -> None:
    """
    Generate comprehensive reports in multiple formats

    Args:
        report_data: Dictionary containing all discovery data
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Generate different report types
    generate_port_analysis_report(report_data, timestamp)
    generate_network_inventory_report(report_data, timestamp)

    print(f"Reports generated with timestamp: {timestamp}")





def generate_port_analysis_report(report_data: Dict[str, Any], timestamp: str) -> None:
    """Generate detailed port analysis report"""
    filename = f"port_analysis_{timestamp}.txt"
    
    with open(filename, 'w') as f:
        f.write("="*100 + "\n")
        f.write("DETAILED PORT ANALYSIS\n")
        f.write("="*100 + "\n")
        f.write(f"Generated: {report_data['timestamp']}\n\n")
        
        for switch_name, switch_data in report_data['switches'].items():
            if not switch_data['connected']:
                continue
            
            f.write(f"SWITCH: {switch_name} ({switch_data['ip']})\n")
            f.write("="*100 + "\n")
            
            # Correlate MAC table, ARP table, and interface status
            port_analysis = correlate_port_data(switch_data)
            
            # Get uplink ports from CDP
            uplink_ports = set(switch_data['cdp_neighbors'].keys())
            
            f.write("PORT ANALYSIS:\n")
            f.write("-" * 15 + "\n")
            f.write(f"{'Port':<15} {'Type':<10} {'Status':<10} {'VLAN':<6} {'MAC Address':<18} {'IP Address':<15} {'Description'}\n")
            f.write("-" * 100 + "\n")
            
            # Process each port
            for port, data in port_analysis.items():
                if port in uplink_ports:
                    neighbor = switch_data['cdp_neighbors'][port]
                    f.write(f"{port:<15} {'Uplink':<10} {'Up':<10} {'-':<6} {'-':<18} {'-':<15} Uplink to {neighbor}\n")
                else:
                    # Regular access port
                    for entry in data:
                        port_type = "Access" if entry['vlan'] != 'trunk' else "Trunk"
                        f.write(f"{port:<15} {port_type:<10} {entry['status']:<10} {entry['vlan']:<6} {entry['mac']:<18} {entry['ip']:<15} {entry['description']}\n")
            
            f.write("\n" + "="*100 + "\n\n")
    
    print(f"Port analysis report saved to: {filename}")


def correlate_port_data(switch_data: Dict[str, Any]) -> Dict[str, List[Dict[str, str]]]:
    """
    Correlate MAC table, ARP table, and interface status data
    
    Args:
        switch_data: Switch data dictionary
        
    Returns:
        Dictionary mapping ports to their connected devices
    """
    port_data = {}
    
    # Process MAC address table
    for mac_entry in switch_data['mac_table']:
        port = mac_entry['ports']
        if port not in port_data:
            port_data[port] = []
        
        # Find corresponding ARP entry
        ip_address = ""
        for arp_entry in switch_data['arp_table']:
            if arp_entry['mac'] == mac_entry['mac']:
                ip_address = arp_entry['ip']
                break
        
        port_data[port].append({
            'vlan': mac_entry['vlan'],
            'mac': mac_entry['mac'],
            'ip': ip_address,
            'status': 'up',  # Default, could be enhanced with interface status parsing
            'description': ''  # Could be enhanced with interface description parsing
        })
    
    return port_data


def generate_network_inventory_report(report_data: Dict[str, Any], timestamp: str) -> None:
    """Generate network mapping in Excel format"""
    try:
        filename = f"network_inventory_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Network mapping (formerly Device Inventory)
            device_inventory = []

            for switch_name, switch_data in report_data['switches'].items():
                if not switch_data['connected']:
                    continue

                # Create MAC to ARP mapping
                mac_to_arp = {}
                for arp_entry in switch_data['arp_table']:
                    mac_to_arp[arp_entry['mac']] = arp_entry

                # Get interface descriptions and port configurations
                interface_descriptions = switch_data.get('interface_descriptions', {})
                port_configurations = switch_data.get('port_configurations', {})

                # Get uplink ports
                uplink_ports = set(switch_data['cdp_neighbors'].keys())

                # Parse interface status to get connected ports
                connected_ports = set()
                interface_status = switch_data.get('interface_status', '')
                for line in interface_status.split('\n'):
                    if 'connected' in line and not line.strip().startswith('Port'):
                        parts = line.split()
                        if parts:
                            port_name = parts[0]
                            connected_ports.add(port_name)

                # Track MAC addresses we've already seen to avoid duplicates from uplinks
                seen_macs = set()
                ports_processed = set()

                # First, process MAC table entries (excluding uplink ports)
                for mac_entry in switch_data['mac_table']:
                    port_name = mac_entry['ports']
                    mac_address = mac_entry['mac']

                    # Skip if this MAC is learned from an uplink port
                    if port_name in uplink_ports:
                        continue

                    # Skip if we've already seen this MAC address
                    if mac_address in seen_macs:
                        continue

                    seen_macs.add(mac_address)
                    ports_processed.add(port_name)

                    arp_info = mac_to_arp.get(mac_address, {})
                    ip_address = arp_info.get('ip', '')

                    # Get interface description
                    port_description = interface_descriptions.get(port_name, '')

                    # Get port configuration
                    port_config = port_configurations.get(port_name, {})
                    port_mode = port_config.get('mode', 'access')

                    # Determine VLANs allowed
                    if port_mode == 'trunk':
                        allowed_vlans = port_config.get('allowed_vlans', 'all')
                        if not allowed_vlans:
                            allowed_vlans = 'all'
                    else:
                        allowed_vlans = port_config.get('access_vlan', '1')

                    # Get native VLAN (only for trunk ports)
                    native_vlan = ''
                    if port_mode == 'trunk':
                        native_vlan = port_config.get('native_vlan', '1')

                    device_inventory.append({
                        'Switch': switch_name,
                        'Port': port_name,
                        'Description': port_description,
                        'Mode': port_mode.title(),
                        'Allowed VLANs': allowed_vlans,
                        'Native VLAN': native_vlan,
                        'VLAN': mac_entry['vlan'],
                        'MAC Address': mac_address,
                        'IP Address': ip_address,
                        'Is Uplink': 'No',
                        'Uplink To': ''
                    })

                # Add uplink ports
                for port_name in uplink_ports:
                    if port_name not in ports_processed:
                        uplink_to = switch_data['cdp_neighbors'].get(port_name, '')
                        port_description = interface_descriptions.get(port_name, '')

                        # Get port configuration
                        port_config = port_configurations.get(port_name, {})
                        port_mode = port_config.get('mode', 'access')

                        # Determine VLANs allowed
                        if port_mode == 'trunk':
                            allowed_vlans = port_config.get('allowed_vlans', 'all')
                            if not allowed_vlans:
                                allowed_vlans = 'all'
                        else:
                            allowed_vlans = port_config.get('access_vlan', '1')

                        # Get native VLAN (only for trunk ports)
                        native_vlan = ''
                        if port_mode == 'trunk':
                            native_vlan = port_config.get('native_vlan', '1')

                        device_inventory.append({
                            'Switch': switch_name,
                            'Port': port_name,
                            'Description': port_description,
                            'Mode': port_mode.title(),
                            'Allowed VLANs': allowed_vlans,
                            'Native VLAN': native_vlan,
                            'VLAN': '',
                            'MAC Address': '',
                            'IP Address': '',
                            'Is Uplink': 'Yes',
                            'Uplink To': uplink_to
                        })
                        ports_processed.add(port_name)

                # Add any other connected ports that don't have MAC addresses
                for port_name in connected_ports:
                    if port_name not in ports_processed and port_name not in uplink_ports:
                        port_description = interface_descriptions.get(port_name, '')

                        # Get port configuration
                        port_config = port_configurations.get(port_name, {})
                        port_mode = port_config.get('mode', 'access')

                        # Determine VLANs allowed
                        if port_mode == 'trunk':
                            allowed_vlans = port_config.get('allowed_vlans', 'all')
                            if not allowed_vlans:
                                allowed_vlans = 'all'
                        else:
                            allowed_vlans = port_config.get('access_vlan', '1')

                        # Get native VLAN (only for trunk ports)
                        native_vlan = ''
                        if port_mode == 'trunk':
                            native_vlan = port_config.get('native_vlan', '1')

                        device_inventory.append({
                            'Switch': switch_name,
                            'Port': port_name,
                            'Description': port_description,
                            'Mode': port_mode.title(),
                            'Allowed VLANs': allowed_vlans,
                            'Native VLAN': native_vlan,
                            'VLAN': '',
                            'MAC Address': '',
                            'IP Address': '',
                            'Is Uplink': 'No',
                            'Uplink To': ''
                        })

            df_devices = pd.DataFrame(device_inventory)
            df_devices.to_excel(writer, sheet_name='Network mapping', index=False)

            # Auto-adjust column widths
            worksheet = writer.sheets['Network mapping']
            for column in df_devices:
                column_length = max(df_devices[column].astype(str).map(len).max(), len(column))
                col_idx = df_devices.columns.get_loc(column)
                worksheet.column_dimensions[chr(65 + col_idx)].width = column_length + 2

        print(f"Excel inventory saved to: {filename}")

    except ImportError:
        print("Warning: pandas/openpyxl not available, skipping Excel report")
    except Exception as e:
        print(f"Error generating Excel report: {e}")


def generate_summary_table(report_data: Dict[str, Any]) -> None:
    """Generate a summary table for console output"""
    switches = report_data['switches']
    
    # Prepare data for table
    table_data = []
    for name, data in switches.items():
        if data['connected']:
            table_data.append([
                name,
                data['ip'],
                'Connected',
                len(data['interfaces']),
                len(data['networks']),
                len(data['cdp_neighbors']),
                len(data['mac_table']),
                len(data['arp_table'])
            ])
        else:
            table_data.append([
                name,
                data['ip'],
                'Failed',
                '-', '-', '-', '-', '-'
            ])
    
    headers = ['Switch', 'IP', 'Status', 'L3 Intf', 'Networks', 'CDP', 'MAC', 'ARP']
    
    print("\nSWITCH SUMMARY:")
    print(tabulate(table_data, headers=headers, tablefmt='grid'))
