#!/usr/bin/env python3
"""
Test script for switch parser functionality
"""

from switch_parser import parse_switch_list, SwitchInfo


def test_switch_list_parsing():
    """Test parsing of the switch_list.txt file"""
    print("Testing switch list parsing...")
    
    switches = parse_switch_list("switch_list.txt")
    
    if not switches:
        print("❌ No switches loaded")
        return False
    
    print(f"✅ Loaded {len(switches)} switches:")
    
    for switch in switches:
        print(f"  - Name: {switch.name}")
        print(f"    IP: {switch.ip}")
        print(f"    Type: {switch.device_type}")
        print(f"    Username: {switch.username}")
        print(f"    Password: {'*' * len(switch.password)}")
        print(f"    Enable: {'*' * len(switch.enable_password)}")
        print()
    
    return True


def test_sample_outputs():
    """Test parsing of sample command outputs"""
    print("Testing command output parsing...")
    
    # Sample show ip interface brief output
    sample_ip_int_brief = """
Interface                  IP-Address      OK? Method Status                Protocol
Vlan1                      unassigned      YES NVRAM  administratively down down    
Vlan10                     ***********     YES NVRAM  up                    up      
Vlan20                     ***********     YES NVRAM  up                    up      
GigabitEthernet0/1         unassigned      YES unset  up                    up      
GigabitEthernet0/2         unassigned      YES unset  down                  down    
"""
    
    from switch_parser import parse_interface_ip
    interfaces = parse_interface_ip(sample_ip_int_brief)
    
    print(f"✅ Parsed {len(interfaces)} Layer 3 interfaces:")
    for interface, ip, status in interfaces:
        print(f"  - {interface}: {ip} ({status})")
    
    # Sample CDP output
    sample_cdp = """
Device ID: sw183-ISP2
Entry address(es): 
  IP address: ************
Platform: cisco WS-C2960-24TT-L,  Capabilities: Switch IGMP 
Interface: GigabitEthernet0/24,  Port ID (outgoing port): GigabitEthernet0/23
Holdtime : 179 sec

Version :
Cisco IOS Software, C2960 Software (C2960-LANBASEK9-M), Version 15.0(2)SE4, RELEASE SOFTWARE (fc1)
"""
    
    from switch_parser import parse_cdp_neighbors
    neighbors = parse_cdp_neighbors(sample_cdp)
    
    print(f"✅ Parsed {len(neighbors)} CDP neighbors:")
    for interface, neighbor in neighbors.items():
        print(f"  - {interface} -> {neighbor}")
    
    return True


def main():
    """Main test function"""
    print("="*50)
    print("SWITCH PARSER TESTS")
    print("="*50)
    
    success = True
    
    # Test switch list parsing
    if not test_switch_list_parsing():
        success = False
    
    print("-" * 50)
    
    # Test command output parsing
    if not test_sample_outputs():
        success = False
    
    print("-" * 50)
    
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return success


if __name__ == "__main__":
    main()
