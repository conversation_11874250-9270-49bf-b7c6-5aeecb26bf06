#!/usr/bin/env python3
"""
Debug script to check interface configuration parsing
"""

from netmiko import ConnectHandler
from switch_parser import parse_switch_list, extract_interface_config, extract_subnet_mask


def debug_interface_config():
    """Debug interface configuration extraction"""
    
    # Load switch info
    switches = parse_switch_list("switch_list.txt")
    if not switches:
        print("No switches found")
        return
    
    switch = switches[0]  # Use first switch
    
    device = {
        'device_type': switch.device_type,
        'host': switch.ip,
        'username': switch.username,
        'password': switch.password,
        'secret': switch.enable_password,
        'timeout': 30,
    }
    
    try:
        with ConnectHandler(**device) as connection:
            print(f"Connected to {switch.name}")
            
            # Get running config
            show_run = connection.send_command("show running-config")

            # First, let's see the IP interface brief output
            print("\nShow IP Interface Brief:")
            ip_int_brief = connection.send_command("show ip interface brief")
            print(ip_int_brief)

            # Look for ALL interface configurations in the config
            print("\nSearching for ALL interface configurations...")
            lines = show_run.split('\n')

            interface_lines = []
            for i, line in enumerate(lines):
                if line.strip().startswith('interface '):
                    print(f"Line {i}: {line}")
                    interface_lines.append((i, line.strip()))

            print(f"\nFound {len(interface_lines)} interface configuration lines")

            # Look specifically for VLAN interfaces
            vlan_interfaces = []
            for i, line in enumerate(lines):
                if 'interface' in line.lower() and 'vlan' in line.lower():
                    print(f"VLAN Line {i}: {line}")
                    vlan_interfaces.append(line.strip())

            print(f"\nFound {len(vlan_interfaces)} VLAN interface lines")
            
            # Test our extraction function
            print("\nTesting interface config extraction for 'Vlan500':")
            config = extract_interface_config(show_run, "Vlan500")
            print(f"Result length: {len(config)}")
            if config:
                print("Config content:")
                print(config)
                print("\nTesting subnet mask extraction:")
                mask = extract_subnet_mask(config)
                print(f"Subnet mask: {mask}")
            else:
                print("No config found!")
                
                # Let's try different variations
                variations = ["vlan500", "Vlan500", "VLAN500", "interface Vlan500"]
                for var in variations:
                    print(f"\nTrying variation: '{var}'")
                    config = extract_interface_config(show_run, var)
                    if config:
                        print(f"Found config with '{var}':")
                        print(config[:200])
                        break
            
            # Show a sample of the running config around VLAN interfaces
            print("\nSample of running config around VLAN interfaces:")
            for i, line in enumerate(lines):
                if 'interface' in line.lower() and 'vlan' in line.lower():
                    start = max(0, i-2)
                    end = min(len(lines), i+10)
                    print(f"\nLines {start}-{end}:")
                    for j in range(start, end):
                        marker = ">>> " if j == i else "    "
                        print(f"{marker}{j:3}: {lines[j]}")
                    break
                    
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    debug_interface_config()
