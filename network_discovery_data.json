{"timestamp": "2025-06-12T21:55:46.047796", "switches": {"sw183-ISP1": {"name": "sw183-ISP1", "ip": "***********", "connected": true, "error": null, "interfaces": [["Vlan500", "***********", "up"]], "networks": ["***********/24"], "cdp_neighbors": {"Gi1/0/10": "sw183-0.canamgroup.canamdc.ws"}, "mac_table": [{"vlan": "500", "mac": "001b.1700.0230", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "3cec.ef82.efc9", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "3cec.ef82.efce", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "3cec.ef82.efd9", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "3cec.ef82.efe5", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "506b.8dfc.783e", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5505.7616", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5530.6d78", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5530.6e96", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5530.6f21", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5530.705f", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "a478.0685.aa90", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "a478.0685.aad0", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "450", "mac": "001b.1700.0210", "type": "STATIC", "ports": "Gi1/0/3"}], "arp_table": [{"protocol": "Internet", "ip": "***********", "age": "246", "mac": "a478.0685.aad0", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********", "age": "8", "mac": "a478.0685.aad0", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********", "age": "-", "mac": "00df.1d22.59c1", "type": "ARPA", "interface": "Vlan500"}], "interface_descriptions": {"Gi1/0/1": "* ISP1 *", "Gi1/0/2": "* ISP1 *", "Gi1/0/3": "* ISP1 *", "Gi1/0/10": "* Trk to sw183-0 *", "Vl500": "* MGMT *"}, "port_configurations": {"Gi1/0/1": {"mode": "access", "access_vlan": "450", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/2": {"mode": "access", "access_vlan": "450", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/3": {"mode": "access", "access_vlan": "450", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/4": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/5": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/6": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/7": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/8": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/9": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/10": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "500", "native_vlan": ""}, "Vl1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl500": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}}, "interface_status": "\nPort      Name               Status       Vlan       Duplex  Speed Type \nGi1/0/1   * ISP1 *           notconnect   450          auto   auto 10/100/1000BaseTX\nGi1/0/2   * ISP1 *           connected    450        a-full a-1000 10/100/1000BaseTX\nGi1/0/3   * ISP1 *           connected    450        a-full a-1000 10/100/1000BaseTX\nGi1/0/4                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/5                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/6                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/7                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/8                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/9                      disabled     1            auto   auto Not Present\nGi1/0/10  * Trk to sw183-0 * connected    trunk      a-full a-1000 10/100/1000BaseTX", "running_config": "Building configuration...\n\nCurrent configuration : 12156 bytes\n!\n! Last configuration change at 08:09:39 EDT Tue Dec 10 2024 by admin\n! NVRAM config last updated at 08:09:40 EDT Tue Dec 10 2024 by admin\n!\nversion 15.2\nno service pad\nservice timestamps debug datetime msec\nservice timestamps log datetime msec\nservice password-encryption\n!\nhostname sw183-ISP1\n!\nboot-start-marker\nboot-end-marker\n!\nenable password 7 0100110D4F5355\n!\nusername admin secret 9 $9$gV54LYH248WE38$CY3srbLsEIU7yLe83TFt9C3mGvZILJJbnf/JTSC4z66\naaa new-model\n!\n!\n!\n!\n!\n!\n!\n!\naaa session-id common\nclock timezone EDT -5 0\nclock summer-time EDT recurring\nswitch 1 provision c1000-8fp-2g-l\nsystem mtu routing 1500\n!\n!\nip dhcp snooping vlan 100-302\nip domain-lookup source-interface Vlan500\nip domain-name canamgroup.canamdc.ws\nip name-server ***********\nip name-server *************\nlogin on-success log\nvtp mode transparent\n!\n!\n!\n!\n!\nmls qos srr-queue output cos-map queue 1 threshold 1 4\nmls qos srr-queue output cos-map queue 2 threshold 1 2 6 7\nmls qos srr-queue output cos-map queue 2 threshold 2 3\nmls qos srr-queue output cos-map queue 3 threshold 2 0\nmls qos srr-queue output cos-map queue 4 threshold 2 1\nmls qos srr-queue output dscp-map queue 1 threshold 2 32 33 40 41 42 43 44 45\nmls qos srr-queue output dscp-map queue 1 threshold 2 46 47\nmls qos srr-queue output dscp-map queue 2 threshold 1 16 17 18 19 20 21 22 23\nmls qos srr-queue output dscp-map queue 2 threshold 1 26 27 28 29 30 31 34 35\nmls qos srr-queue output dscp-map queue 2 threshold 1 36 37 38 39\nmls qos srr-queue output dscp-map queue 2 threshold 2 24 48 49 50 51 52 53 54\nmls qos srr-queue output dscp-map queue 2 threshold 2 55 56 57 58 59 60 61 62\nmls qos srr-queue output dscp-map queue 2 threshold 2 63\nmls qos srr-queue output dscp-map queue 3 threshold 1 0 1 2 3 4 5 6 7\nmls qos srr-queue output dscp-map queue 4 threshold 1 8 9 10 11 12 13 14 15\nmls qos\n!\ncrypto pki trustpoint SLA-TrustPoint\n revocation-check crl\n!\ncrypto pki trustpoint TP-self-signed-488790400\n enrollment selfsigned\n subject-name cn=IOS-Self-Signed-Certificate-488790400\n revocation-check none\n rsakeypair TP-self-signed-488790400\n!\n!\ncrypto pki certificate chain SLA-TrustPoint\ncrypto pki certificate chain TP-self-signed-488790400\n certificate self-signed 01\n  30820229 30820192 ******** ******** 300D0609 2A864886 F70D0101 05050030 \n  30312E30 2C060355 04031325 494F532D 53656C66 2D536967 6E65642D 43657274 \n  69666963 6174652D 34383837 39303430 30301E17 0D323330 33333131 33313734 \n  345A170D 33303031 30313030 30303030 5A303031 2E302C06 03550403 1325494F \n  532D5365 6C662D53 69676E65 642D4365 72746966 69636174 652D3438 38373930 \n  34303030 819F300D 06092A86 4886F70D 01010105 0003818D 00308189 02818100 \n  A3BE4A0F CA99930A CE7C2551 1B1F4FF4 9CE0E58D 2A3E4D55 CD1CAF35 D875A98C \n  A57679EF C949EAD4 78959F5C 8AC6B9C1 3DDF8825 0022E869 B15741D9 8055A614 \n  61C75B1C 3FE6F3C6 F408020F 936650CE 71758A84 E55BFBD6 FE96C317 5762A131 \n  1B5FA5E0 D75466A7 82F2FE40 794C2EA7 E9D84960 9655A1F3 90573FE4 62EE7C8D \n  02030100 01A35330 51300F06 03551D13 0101FF04 05300301 01FF301F 0603551D \n  23041830 16801481 9509574F D147FD39 9D18D9E6 6FB688E6 81140D30 1D060355 \n  1D0E0416 04148195 09574FD1 47FD399D 18D9E66F B688E681 140D300D 06092A86 \n  4886F70D 01010505 00038181 009A7E5A 236F1CA7 9D6ED640 96891875 9606E1F4 \n  324472F4 9F207C48 4ABEE9EC F715DD79 D2566FB3 3F3D51CF 3ACA2AB7 BD9F088B \n  AF911BDE DDBEFF15 5D0F0535 34EE3525 A37B4F0D C84CDAED 7872A275 DD9F459D \n  AEF28B0C 4770A7F1 97108346 161A314B A516D183 1B561487 F8BE1A28 D033B5C2 \n  4E73EDD9 FBFBE92C 0BF9DEE8 77\n  \tquit\narchive\n path ftp://***********/$h$t.cfg\n write-memory\n time-period 1440\nmemory free low-watermark processor 23433\n!\nspanning-tree mode rapid-pvst\nspanning-tree loopguard default\nspanning-tree portfast edge default\nspanning-tree portfast edge bpduguard default\nspanning-tree extend system-id\nspanning-tree uplinkfast\nspanning-tree backbonefast\nspanning-tree vlan 500 priority 36864\nauto qos srnd4\nerrdisable recovery cause udld\nerrdisable recovery cause bpduguard\nerrdisable recovery cause security-violation\nerrdisable recovery cause pagp-flap\nerrdisable recovery cause dtp-flap\nerrdisable recovery cause link-flap\nerrdisable recovery cause sfp-config-mismatch\nerrdisable recovery cause gbic-invalid\nerrdisable recovery cause psecure-violation\nerrdisable recovery cause dhcp-rate-limit\nerrdisable recovery cause storm-control\nerrdisable recovery cause arp-inspection\nerrdisable recovery cause loopback\nerrdisable recovery interval 60\n!\nvlan internal allocation policy ascending\n!\nvlan 450\n name ISP1\n!\nvlan 500\n name MGMT\n!\n!\nclass-map match-any system-cpp-police-ewlc-control\n  description EWLC Control \nclass-map match-any system-cpp-police-topology-control\n  description Topology control\nclass-map match-any system-cpp-police-sw-forward\n  description Sw forwarding, L2 LVX data packets, LOGGING, Transit Traffic\nclass-map match-any system-cpp-default\n  description EWLC data, Inter FED Traffic \nclass-map match-any system-cpp-police-sys-data\n  description Openflow, Exception, EGR Exception, NFL Sampled Data, RPF Failed\nclass-map match-any system-cpp-police-punt-webauth\n  description Punt Webauth\nclass-map match-any system-cpp-police-l2lvx-control\n  description L2 LVX control packets\nclass-map match-any system-cpp-police-forus\n  description Forus Address resolution and Forus traffic\nclass-map match-any system-cpp-police-multicast-end-station\n  description MCAST END STATION\nclass-map match-any system-cpp-police-high-rate-app\n  description High Rate Applications \nclass-map match-any system-cpp-police-multicast\n  description MCAST Data\nclass-map match-any system-cpp-police-l2-control\n  description L2 control\nclass-map match-any system-cpp-police-dot1x-auth\n  description DOT1X Auth\nclass-map match-any system-cpp-police-data\n  description ICMP redirect, ICMP_GEN and BROADCAST\nclass-map match-any system-cpp-police-stackwise-virt-control\n  description Stackwise Virtual OOB\nclass-map match-any non-client-nrt-class\nclass-map match-any system-cpp-police-routing-control\n  description Routing control and Low Latency\nclass-map match-any system-cpp-police-protocol-snooping\n  description Protocol snooping\nclass-map match-any system-cpp-police-dhcp-snooping\n  description DHCP snooping\nclass-map match-any system-cpp-police-ios-routing\n  description L2 control, Topology control, Routing control, Low Latency\nclass-map match-any system-cpp-police-system-critical\n  description System Critical and Gold Pkt\nclass-map match-any system-cpp-police-ios-feature\n  description ICMPGEN,BROADCAST,ICMP,L2LVXCntrl,ProtoSnoop,PuntWebauth,MCASTData,Transit,DOT1XAuth,Swfwd,LOGGING,L2LVXData,ForusTraffic,ForusARP,McastEndStn,Openflow,Exception,EGRExcption,NflSampled,RpfFailed\n!\n!\n!\nmacro name APS\n switchport access vlan 300\n switchport mode access\n switchport nonegotiate\n srr-queue bandwidth share 10 10 60 20\n srr-queue bandwidth shape  10  0  0  0\n queue-set 2\n mls qos trust device cisco-phone\n mls qos trust dscp\n auto qos voip cisco-phone\n storm-control broadcast level 1.00\n spanning-tree portfast\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n@\nmacro name TRUNK\n description * Trunk to <sw_name> *\n switchport trunk encapsulation dot1q\n switchport trunk allowed vlan all\n switchport mode trunk\n switchport nonegotiate\n srr-queue bandwidth share 10 10 60 20\n srr-queue bandwidth shape  10  0  0  0\n queue-set 2\n auto qos voip trust\n mls qos trust dscp\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n ip dhcp snooping trust\n no shutdown\n mls qos trust dscp\n@\nmacro name NO\n no description\n no switchport access vlan 100\n no switchport trunk encapsulation dot1q\n switchport mode access\n no switchport trunk allowed vlan 100,300-302\n no switchport voice vlan 301\n no switchport nonegotiate\n no switchport port-security\n no switchport port-security maximum 3\n no switchport port-security aging time 2\n no switchport port-security violation restrict\n no switchport port-security aging type inactivity\n no srr-queue bandwidth share 10 10 60 20\n no srr-queue bandwidth shape  10  0  0  0\n no queue-set 2\n no mls qos trust dscp\n no mls qos trust device cisco-phone\n no macro description\n no auto qos voip trust\n no storm-control broadcast level 1.00\n no spanning-tree portfast disable\n no spanning-tree bpdufilter disable\n no spanning-tree bpduguard disable\n no spanning-tree guard\n no ip dhcp snooping limit rate 5\n no ip dhcp snooping trust\n@\nmacro name VOICE\n description * Voice et Data *\n switchport mode access\n switchport nonegotiate\n switchport access vlan 100\n switchport voice vlan 301\n switchport port-security\n switchport port-security maximum 3\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n spanning-tree guard root\n switchport priority extend cos 0\n auto qos voip cisco-phone\n mls qos trust dscp\n mls qos trust device cisco-phone\n storm-control broadcast level 1.00\n ip dhcp snooping limit rate 5\n no shutdown\n@\nmacro name DATA\n description * Data *\n switchport mode access\n switchport nonegotiate\n switchport access vlan  100\n switchport port-security\n switchport port-security maximum 1\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n spanning-tree guard root\n mls qos trust dscp\n storm-control broadcast level 1.00\n ip dhcp snooping limit rate 5\n no shutdown\n@\n!\n!\ninterface GigabitEthernet1/0/1\n description * ISP1 *\n switchport access vlan 450\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n mls qos trust dscp\n macro description DATA\n storm-control broadcast level 1.00\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/2\n description * ISP1 *\n switchport access vlan 450\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n mls qos trust dscp\n macro description DATA\n storm-control broadcast level 1.00\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/3\n description * ISP1 *\n switchport access vlan 450\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n mls qos trust dscp\n macro description DATA\n storm-control broadcast level 1.00\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/4\n shutdown\n!\ninterface GigabitEthernet1/0/5\n shutdown\n!\ninterface GigabitEthernet1/0/6\n shutdown\n!\ninterface GigabitEthernet1/0/7\n shutdown\n!\ninterface GigabitEthernet1/0/8\n shutdown\n!\ninterface GigabitEthernet1/0/9\n shutdown\n!\ninterface GigabitEthernet1/0/10\n description * Trk to sw183-0 *\n switchport trunk allowed vlan 500\n switchport mode trunk\n switchport nonegotiate\n mls qos trust cos\n srr-queue bandwidth share 1 30 35 5\n srr-queue bandwidth shape  10 0 0 0\n priority-queue out \n macro description TRUNK\n auto qos trust \n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n ip dhcp snooping trust\n!\ninterface Vlan1\n no ip address\n shutdown\n!\ninterface Vlan500\n description * MGMT *\n ip address *********** *************\n!\nip default-gateway ***********\nip ftp username userbk\nip ftp password 7 104C0C15091201340F052428213A206625121513\nip http server\nip http banner\nip http authentication local\nip http secure-server\nip ssh time-out 100\nip ssh authentication-retries 4\nip ssh version 2\n!\naccess-list 1 permit 10.0.0.0 *************\n!\n!\n!\n!\n!\nline con 0\n stopbits 1\nline vty 0 4\n access-class 1 in\n exec-timeout 30 0\n length 25\n transport input ssh\nline vty 5 15\n access-class 1 in\n exec-timeout 30 0\n length 25\n transport input ssh\n!\nntp server **********\nntp server **********\nend\n"}, "sw183-ISP2": {"name": "sw183-ISP2", "ip": "************", "connected": true, "error": null, "interfaces": [["Vlan500", "************", "up"]], "networks": ["***********/24"], "cdp_neighbors": {"Gi1/0/10": "sw183-0.canamgroup.canamdc.ws"}, "mac_table": [{"vlan": "500", "mac": "001b.1700.0230", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "3cec.ef82.efc9", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "3cec.ef82.efce", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "3cec.ef82.efd9", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "3cec.ef82.efe5", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "506b.8dfc.783e", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5505.7616", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5530.6d78", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5530.6e96", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5530.6f21", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "7cc2.5530.705f", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "a478.0685.aad0", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "500", "mac": "a478.0689.4e90", "type": "DYNAMIC", "ports": "Gi1/0/10"}, {"vlan": "451", "mac": "001b.1700.0211", "type": "STATIC", "ports": "Gi1/0/2"}], "arp_table": [{"protocol": "Internet", "ip": "***********", "age": "195", "mac": "a478.0685.aad0", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "************", "age": "-", "mac": "00df.1d22.1b41", "type": "ARPA", "interface": "Vlan500"}], "interface_descriptions": {"Gi1/0/1": "* ISP2 *", "Gi1/0/2": "* ISP2 *", "Gi1/0/3": "* ISP2 *", "Gi1/0/10": "* Trk to sw143-0 *", "Vl500": "* MGMT *"}, "port_configurations": {"Gi1/0/1": {"mode": "access", "access_vlan": "451", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/2": {"mode": "access", "access_vlan": "451", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/3": {"mode": "access", "access_vlan": "451", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/4": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/5": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/6": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/7": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/8": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/9": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/10": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "500", "native_vlan": ""}, "Vl1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl500": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}}, "interface_status": "\nPort      Name               Status       Vlan       Duplex  Speed Type \nGi1/0/1   * ISP2 *           notconnect   451          auto   auto 10/100/1000BaseTX\nGi1/0/2   * ISP2 *           connected    451        a-full a-1000 10/100/1000BaseTX\nGi1/0/3   * ISP2 *           connected    451        a-full a-1000 10/100/1000BaseTX\nGi1/0/4                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/5                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/6                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/7                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/8                      disabled     1            auto   auto 10/100/1000BaseTX\nGi1/0/9                      disabled     1            auto   auto Not Present\nGi1/0/10  * Trk to sw143-0 * connected    trunk      a-full a-1000 10/100/1000BaseTX", "running_config": "Building configuration...\n\nCurrent configuration : 11865 bytes\n!\n! Last configuration change at 07:11:50 EDT Fri Nov 17 2023\n!\nversion 15.2\nno service pad\nservice timestamps debug datetime msec\nservice timestamps log datetime msec\nservice password-encryption\n!\nhostname sw183-ISP2\n!\nboot-start-marker\nboot-end-marker\n!\nenable secret 9 $9$rh5XVfy4GSiQCH$/vM9U5WOLW1SS8zIAIm9WP.yWFCkPEXuLO7YcDq8E3I\nenable password 7 0100110D4F5355\n!\nusername admin privilege 15 password 7 0215130A5355\naaa new-model\n!\n!\n!\n!\n!\n!\n!\n!\naaa session-id common\nclock timezone EDT -5 0\nclock summer-time EDT recurring\nswitch 1 provision c1000-8fp-2g-l\nsystem mtu routing 1500\n!\n!\nip dhcp snooping vlan 100-302\nip domain-lookup source-interface Vlan500\nip domain-name canamgroupinc.com\nip name-server ***********\nip name-server *************\nlogin on-success log\nvtp mode transparent\n!\n!\n!\n!\n!\nmls qos srr-queue output cos-map queue 1 threshold 1 4\nmls qos srr-queue output cos-map queue 2 threshold 1 2 6 7\nmls qos srr-queue output cos-map queue 2 threshold 2 3\nmls qos srr-queue output cos-map queue 3 threshold 2 0\nmls qos srr-queue output cos-map queue 4 threshold 2 1\nmls qos srr-queue output dscp-map queue 1 threshold 2 32 33 40 41 42 43 44 45\nmls qos srr-queue output dscp-map queue 1 threshold 2 46 47\nmls qos srr-queue output dscp-map queue 2 threshold 1 16 17 18 19 20 21 22 23\nmls qos srr-queue output dscp-map queue 2 threshold 1 26 27 28 29 30 31 34 35\nmls qos srr-queue output dscp-map queue 2 threshold 1 36 37 38 39\nmls qos srr-queue output dscp-map queue 2 threshold 2 24 48 49 50 51 52 53 54\nmls qos srr-queue output dscp-map queue 2 threshold 2 55 56 57 58 59 60 61 62\nmls qos srr-queue output dscp-map queue 2 threshold 2 63\nmls qos srr-queue output dscp-map queue 3 threshold 1 0 1 2 3 4 5 6 7\nmls qos srr-queue output dscp-map queue 4 threshold 1 8 9 10 11 12 13 14 15\nmls qos\n!\ncrypto pki trustpoint SLA-TrustPoint\n revocation-check crl\n!\ncrypto pki trustpoint TP-self-signed-488774400\n enrollment selfsigned\n subject-name cn=IOS-Self-Signed-Certificate-488774400\n revocation-check none\n rsakeypair TP-self-signed-488774400\n!\n!\ncrypto pki certificate chain SLA-TrustPoint\ncrypto pki certificate chain TP-self-signed-488774400\n certificate self-signed 01\n  30820229 30820192 ******** ******** 300D0609 2A864886 F70D0101 05050030 \n  30312E30 2C060355 04031325 494F532D 53656C66 2D536967 6E65642D 43657274 \n  69666963 6174652D 34383837 37343430 30301E17 0D323330 33333131 33333031 \n  375A170D 33303031 30313030 30303030 5A303031 2E302C06 03550403 1325494F \n  532D5365 6C662D53 69676E65 642D4365 72746966 69636174 652D3438 38373734 \n  34303030 819F300D 06092A86 4886F70D 01010105 0003818D 00308189 02818100 \n  AF010F4D 7075E558 B9C57EEC 1F57A5B8 1E1415D2 7CC2EF24 64E48ABF D1A62BD9 \n  83940F1A FB247F81 B48145F6 B415E2A4 9CD2719B 034FA3F3 0E77FA21 F0A773E0 \n  8F5C473F 5837E307 970C7416 12358EA5 B6A5DB1C 27004CCF 1EDA130D EBC698A4 \n  00423C2A 39182816 84728A49 FF547F9E E9043CFD B9CA4163 CA1EE403 51427B1B \n  02030100 01A35330 51300F06 03551D13 0101FF04 05300301 01FF301F 0603551D \n  23041830 168014D9 BBC3BD76 9E7D3D33 E6FD302B 9FA46F65 6E029D30 1D060355 \n  1D0E0416 0414D9BB C3BD769E 7D3D33E6 FD302B9F A46F656E 029D300D 06092A86 \n  4886F70D 01010505 00038181 002B1870 0941D934 E5546B86 F6C6DC38 F074CAF6 \n  23D70FD7 A9930B81 813937D6 874D5B02 7E5DA591 CF8B707D BB342EA0 2CD47D77 \n  BFC9B0C2 C10858D6 50FE4A6C 75AE2DB8 52E308EF 1C1D956B 1B4A7B44 E00E7E71 \n  61564367 ACEA9C7F 79F0C0ED 10AE7419 2E030610 0D1FAD23 4E8B990F 69453D22 \n  B63BF733 7077DD44 72CC7E27 08\n  \tquit\nmemory free low-watermark processor 23433\n!\nspanning-tree mode rapid-pvst\nspanning-tree loopguard default\nspanning-tree portfast edge default\nspanning-tree portfast edge bpduguard default\nspanning-tree extend system-id\nspanning-tree uplinkfast\nspanning-tree backbonefast\nspanning-tree vlan 500 priority 36864\nauto qos srnd4\nerrdisable recovery cause udld\nerrdisable recovery cause bpduguard\nerrdisable recovery cause security-violation\nerrdisable recovery cause pagp-flap\nerrdisable recovery cause dtp-flap\nerrdisable recovery cause link-flap\nerrdisable recovery cause sfp-config-mismatch\nerrdisable recovery cause gbic-invalid\nerrdisable recovery cause psecure-violation\nerrdisable recovery cause dhcp-rate-limit\nerrdisable recovery cause storm-control\nerrdisable recovery cause arp-inspection\nerrdisable recovery cause loopback\nerrdisable recovery interval 60\n!\nvlan internal allocation policy ascending\n!\nvlan 451\n name ISP2\n!\nvlan 500\n name MGMT\n!\n!\nclass-map match-any system-cpp-police-ewlc-control\n  description EWLC Control \nclass-map match-any system-cpp-police-topology-control\n  description Topology control\nclass-map match-any system-cpp-police-sw-forward\n  description Sw forwarding, L2 LVX data packets, LOGGING, Transit Traffic\nclass-map match-any system-cpp-default\n  description EWLC data, Inter FED Traffic \nclass-map match-any system-cpp-police-sys-data\n  description Openflow, Exception, EGR Exception, NFL Sampled Data, RPF Failed\nclass-map match-any system-cpp-police-punt-webauth\n  description Punt Webauth\nclass-map match-any system-cpp-police-l2lvx-control\n  description L2 LVX control packets\nclass-map match-any system-cpp-police-forus\n  description Forus Address resolution and Forus traffic\nclass-map match-any system-cpp-police-multicast-end-station\n  description MCAST END STATION\nclass-map match-any system-cpp-police-high-rate-app\n  description High Rate Applications \nclass-map match-any system-cpp-police-multicast\n  description MCAST Data\nclass-map match-any system-cpp-police-l2-control\n  description L2 control\nclass-map match-any system-cpp-police-dot1x-auth\n  description DOT1X Auth\nclass-map match-any system-cpp-police-data\n  description ICMP redirect, ICMP_GEN and BROADCAST\nclass-map match-any system-cpp-police-stackwise-virt-control\n  description Stackwise Virtual OOB\nclass-map match-any non-client-nrt-class\nclass-map match-any system-cpp-police-routing-control\n  description Routing control and Low Latency\nclass-map match-any system-cpp-police-protocol-snooping\n  description Protocol snooping\nclass-map match-any system-cpp-police-dhcp-snooping\n  description DHCP snooping\nclass-map match-any system-cpp-police-ios-routing\n  description L2 control, Topology control, Routing control, Low Latency\nclass-map match-any system-cpp-police-system-critical\n  description System Critical and Gold Pkt\nclass-map match-any system-cpp-police-ios-feature\n  description ICMPGEN,BROADCAST,ICMP,L2LVXCntrl,ProtoSnoop,PuntWebauth,MCASTData,Transit,DOT1XAuth,Swfwd,LOGGING,L2LVXData,ForusTraffic,ForusARP,McastEndStn,Openflow,Exception,EGRExcption,NflSampled,RpfFailed\n!\n!\n!\nmacro name APS\n switchport access vlan 300\n switchport mode access\n switchport nonegotiate\n srr-queue bandwidth share 10 10 60 20\n srr-queue bandwidth shape  10  0  0  0\n queue-set 2\n mls qos trust device cisco-phone\n mls qos trust dscp\n auto qos voip cisco-phone\n storm-control broadcast level 1.00\n spanning-tree portfast\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n@\nmacro name TRUNK\n description * Trunk to <sw_name> *\n switchport trunk encapsulation dot1q\n switchport trunk allowed vlan all\n switchport mode trunk\n switchport nonegotiate\n srr-queue bandwidth share 10 10 60 20\n srr-queue bandwidth shape  10  0  0  0\n queue-set 2\n auto qos voip trust\n mls qos trust dscp\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n ip dhcp snooping trust\n no shutdown\n mls qos trust dscp\n@\nmacro name NO\n no description\n no switchport access vlan 100\n no switchport trunk encapsulation dot1q\n switchport mode access\n no switchport trunk allowed vlan 100,300-302\n no switchport voice vlan 301\n no switchport nonegotiate\n no switchport port-security\n no switchport port-security maximum 3\n no switchport port-security aging time 2\n no switchport port-security violation restrict\n no switchport port-security aging type inactivity\n no srr-queue bandwidth share 10 10 60 20\n no srr-queue bandwidth shape  10  0  0  0\n no queue-set 2\n no mls qos trust dscp\n no mls qos trust device cisco-phone\n no macro description\n no auto qos voip trust\n no storm-control broadcast level 1.00\n no spanning-tree portfast disable\n no spanning-tree bpdufilter disable\n no spanning-tree bpduguard disable\n no spanning-tree guard\n no ip dhcp snooping limit rate 5\n no ip dhcp snooping trust\n@\nmacro name VOICE\n description * Voice et Data *\n switchport mode access\n switchport nonegotiate\n switchport access vlan 100\n switchport voice vlan 301\n switchport port-security\n switchport port-security maximum 3\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n spanning-tree guard root\n switchport priority extend cos 0\n auto qos voip cisco-phone\n mls qos trust dscp\n mls qos trust device cisco-phone\n storm-control broadcast level 1.00\n ip dhcp snooping limit rate 5\n no shutdown\n@\nmacro name DATA\n description * Data *\n switchport mode access\n switchport nonegotiate\n switchport access vlan  100\n switchport port-security\n switchport port-security maximum 1\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n spanning-tree guard root\n mls qos trust dscp\n storm-control broadcast level 1.00\n ip dhcp snooping limit rate 5\n no shutdown\n@\n!\n!\ninterface GigabitEthernet1/0/1\n description * ISP2 *\n switchport access vlan 451\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n mls qos trust dscp\n macro description DATA\n storm-control broadcast level 1.00\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/2\n description * ISP2 *\n switchport access vlan 451\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n mls qos trust dscp\n macro description DATA\n storm-control broadcast level 1.00\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/3\n description * ISP2 *\n switchport access vlan 451\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n mls qos trust dscp\n macro description DATA\n storm-control broadcast level 1.00\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/4\n shutdown\n!\ninterface GigabitEthernet1/0/5\n shutdown\n!\ninterface GigabitEthernet1/0/6\n shutdown\n!\ninterface GigabitEthernet1/0/7\n shutdown\n!\ninterface GigabitEthernet1/0/8\n shutdown\n!\ninterface GigabitEthernet1/0/9\n shutdown\n!\ninterface GigabitEthernet1/0/10\n description * Trk to sw143-0 *\n switchport trunk allowed vlan 500\n switchport mode trunk\n switchport nonegotiate\n mls qos trust cos\n srr-queue bandwidth share 1 30 35 5\n srr-queue bandwidth shape  10 0 0 0\n priority-queue out \n macro description TRUNK\n auto qos trust \n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n ip dhcp snooping trust\n!\ninterface Vlan1\n no ip address\n shutdown\n!\ninterface Vlan500\n description * MGMT *\n ip address ************ *************\n!\nip default-gateway ***********\nip http server\nip http banner\nip http authentication local\nip http secure-server\n!\naccess-list 1 permit 10.0.0.0 *************\n!\n!\n!\n!\n!\nline con 0\n stopbits 1\nline vty 0 4\n access-class 1 in\n exec-timeout 30 0\n length 25\n transport input telnet ssh\nline vty 5 15\n access-class 1 in\n exec-timeout 30 0\n length 25\n transport input telnet ssh\n!\nend\n"}, "sw183-0": {"name": "sw183-0", "ip": "***********", "connected": true, "error": null, "interfaces": [["Vlan11", "************", "up"], ["Vlan100", "**********", "up"], ["Vlan110", "***********", "up"], ["Vlan115", "***********", "up"], ["Vlan160", "***********", "up"], ["Vlan161", "**********", "up"], ["Vlan170", "**********", "up"], ["Vlan500", "***********", "up"]], "networks": ["**********/28", "*********/22", "***********/24", "***********/22", "**********/24", "**********/24", "*********/22", "**********/22"], "cdp_neighbors": {"Gi1/0/24": "sw183-1.canamgroupinc.com", "Gi1/0/16": "sw183-ISP1.canamgroup.canamdc.ws", "Te1/1/8": "Nx183-1.canamgroupinc.com(FDO253304ZV)", "Te2/1/8": "Nx183-2.canamgroupinc.com(FDO253304ZQ)", "Gi2/0/16": "sw183-ISP2.canamgroupinc.com", "Gi1/0/22": "SW183-3.canamgroup.canamdc.ws", "Gi2/0/20": "sw183-2.canamgroup.canamdc.ws", "Te1/1/7": "sw183-4.canamgroup.canamdc.ws", "Gi2/0/1": "Ro183-2.canam.ws", "Gi1/0/1": "Ro183-1.canam.ws", "Gi2/0/17": "brasov-wlc1"}, "mac_table": [{"vlan": "1", "mac": "0026.f002.0000", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "1", "mac": "5835.d9b6.c798", "type": "DYNAMIC", "ports": "Gi1/0/24"}, {"vlan": "1", "mac": "5835.d9e4.5c18", "type": "DYNAMIC", "ports": "Gi2/0/20"}, {"vlan": "1", "mac": "647c.e86d.b012", "type": "DYNAMIC", "ports": "Po4"}, {"vlan": "1", "mac": "647c.e86d.b013", "type": "DYNAMIC", "ports": "Po4"}, {"vlan": "1", "mac": "647c.e872.2412", "type": "DYNAMIC", "ports": "Po3"}, {"vlan": "1", "mac": "647c.e872.2413", "type": "DYNAMIC", "ports": "Po3"}, {"vlan": "1", "mac": "b8be.bf2a.9b81", "type": "DYNAMIC", "ports": "Gi1/0/22"}, {"vlan": "1", "mac": "bc4a.56f1.81b1", "type": "DYNAMIC", "ports": "Te1/1/7"}, {"vlan": "1", "mac": "d0e0.42ad.ca27", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "1", "mac": "d0e0.42ad.ca57", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "1", "mac": "d0e0.42ad.ca77", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "1", "mac": "d0e0.42ad.caa7", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "1", "mac": "f403.43d5.7eb0", "type": "DYNAMIC", "ports": "Te2/1/2"}, {"vlan": "1", "mac": "f403.43d5.7eb8", "type": "DYNAMIC", "ports": "Te1/1/1"}, {"vlan": "1", "mac": "f403.43d5.a440", "type": "DYNAMIC", "ports": "Te1/1/2"}, {"vlan": "1", "mac": "f403.43d5.a448", "type": "DYNAMIC", "ports": "Te2/1/1"}, {"vlan": "11", "mac": "506b.8d87.2602", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8d8a.0636", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8d8a.2ca4", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8d8a.5b85", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8dc1.5769", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8dd1.d9d4", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8ddb.3c70", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8ddd.362a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8dde.1b51", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8df6.cac8", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "506b.8dfc.783e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "11", "mac": "a478.0685.aad4", "type": "STATIC", "ports": "Vl11"}, {"vlan": "100", "mac": "0006.6743.d868", "type": "DYNAMIC", "ports": "Te1/1/7"}, {"vlan": "100", "mac": "0017.c8dc.12d3", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "001a.6e01.0642", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "001b.1700.0230", "type": "DYNAMIC", "ports": "Po4"}, {"vlan": "100", "mac": "0050.5693.0367", "type": "DYNAMIC", "ports": "Te2/1/2"}, {"vlan": "100", "mac": "0050.5693.0609", "type": "DYNAMIC", "ports": "Te2/1/2"}, {"vlan": "100", "mac": "0050.5693.3655", "type": "DYNAMIC", "ports": "Te2/1/2"}, {"vlan": "100", "mac": "0050.5693.3dee", "type": "DYNAMIC", "ports": "Te2/1/2"}, {"vlan": "100", "mac": "0050.5693.5069", "type": "DYNAMIC", "ports": "Te1/1/2"}, {"vlan": "100", "mac": "0050.5693.6032", "type": "DYNAMIC", "ports": "Te1/1/2"}, {"vlan": "100", "mac": "0050.5693.6394", "type": "DYNAMIC", "ports": "Te1/1/2"}, {"vlan": "100", "mac": "0050.5693.653f", "type": "DYNAMIC", "ports": "Te1/1/2"}, {"vlan": "100", "mac": "0050.5693.6b0e", "type": "DYNAMIC", "ports": "Te1/1/2"}, {"vlan": "100", "mac": "0050.5693.8212", "type": "DYNAMIC", "ports": "Te1/1/2"}, {"vlan": "100", "mac": "0050.5693.d02f", "type": "DYNAMIC", "ports": "Te2/1/2"}, {"vlan": "100", "mac": "0050.5693.e410", "type": "DYNAMIC", "ports": "Te2/1/2"}, {"vlan": "100", "mac": "0050.56a8.804f", "type": "DYNAMIC", "ports": "Te2/1/2"}, {"vlan": "100", "mac": "0050.56a8.f2c7", "type": "DYNAMIC", "ports": "Te2/1/2"}, {"vlan": "100", "mac": "0068.eb9c.3b18", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.eba5.3e5e", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.eba5.3f5c", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.eba5.3fc8", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.ebaa.46e3", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.ebaa.46e5", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.ebaa.46eb", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.ebae.ad22", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.ebae.ad43", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.ebb6.55c0", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.ebb6.56d9", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0068.ebb6.56db", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "00c0.ff52.1e7a", "type": "STATIC", "ports": "Gi1/0/15"}, {"vlan": "100", "mac": "00c0.ff52.24b1", "type": "DYNAMIC", "ports": "Gi1/0/22"}, {"vlan": "100", "mac": "0409.73b1.f200", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "04bf.1b16.9d78", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "04bf.1b29.86d8", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "04bf.1b29.89f2", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "04bf.1b29.d51d", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "04bf.1b62.8599", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "04d5.9005.3bc8", "type": "STATIC", "ports": "Gi1/0/11"}, {"vlan": "100", "mac": "0c37.9638.ac5c", "type": "DYNAMIC", "ports": "Gi2/0/20"}, {"vlan": "100", "mac": "0c37.9669.cb59", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0c97.5f24.1240", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0c97.5f24.6020", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0c97.5f24.e820", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "0c97.5f2c.7dc0", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "1860.2487.9303", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "1860.2487.9305", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "1860.2488.c9cd", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "1860.2488.c9d5", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "1860.248a.4232", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "1860.248a.423c", "type": "DYNAMIC", "ports": "Gi1/0/24"}, {"vlan": "100", "mac": "1860.248a.423e", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "1860.24ae.deb8", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "207b.d26a.e1e8", "type": "DYNAMIC", "ports": "Te1/1/7"}, {"vlan": "100", "mac": "2800.af19.9d6d", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "2829.864a.a436", "type": "DYNAMIC", "ports": "Gi1/0/22"}, {"vlan": "100", "mac": "2829.8653.b643", "type": "DYNAMIC", "ports": "Te1/1/7"}, {"vlan": "100", "mac": "2880.2310.2869", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "3464.a9a2.4300", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "3464.a9a2.53b8", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "3473.5aa8.27e3", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "3814.28b0.d0b6", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "3822.e2d7.fa46", "type": "DYNAMIC", "ports": "Gi2/0/20"}, {"vlan": "100", "mac": "3863.bb55.9bc0", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "3863.bb5d.5380", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "3863.bb67.20c0", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "445b.ed16.eb80", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "48ea.622b.2dcd", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "48ea.622d.c2a8", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "5820.b1f4.a200", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "5820.b1fa.6d80", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "5835.d9b6.c7c1", "type": "DYNAMIC", "ports": "Gi1/0/24"}, {"vlan": "100", "mac": "5835.d9e4.5c41", "type": "DYNAMIC", "ports": "Gi2/0/20"}, {"vlan": "100", "mac": "60e8.5b0a.5978", "type": "DYNAMIC", "ports": "Te1/1/7"}, {"vlan": "100", "mac": "64c9.01a8.7626", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "64f6.9d5e.b16b", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "64f6.9d5e.b200", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "6c3b.e516.b0fc", "type": "DYNAMIC", "ports": "Gi2/0/20"}, {"vlan": "100", "mac": "74a0.2fe0.7b20", "type": "DYNAMIC", "ports": "Gi2/0/1"}, {"vlan": "100", "mac": "80e8.2cd7.e6e1", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cd7.e6fb", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cd8.3f5a", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2ce3.a639", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2ce3.a6df", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cec.290c", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cec.33a6", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cec.3810", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cec.381c", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cec.382d", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cec.38df", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cec.38f3", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cec.3902", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cf6.c6d7", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cf6.c6db", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "80e8.2cf6.c6e3", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "8480.2df9.6a20", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "100", "mac": "8480.2df9.6a24", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "100", "mac": "84a9.3e63.9781", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "84a9.3e63.9799", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "84a9.3e63.9985", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "84a9.3e6c.76df", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "84a9.3e6c.7870", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "84a9.3e6c.7e27", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "84a9.3e6c.7e2a", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "84a9.3e73.9a43", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "84a9.3e7e.e893", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "84f1.47fa.0528", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9009.d00c.b082", "type": "DYNAMIC", "ports": "Gi1/0/24"}, {"vlan": "100", "mac": "9440.c92f.a224", "type": "STATIC", "ports": "Gi1/0/14"}, {"vlan": "100", "mac": "9440.c92f.e254", "type": "STATIC", "ports": "Gi2/0/9"}, {"vlan": "100", "mac": "9440.c942.768e", "type": "DYNAMIC", "ports": "Gi1/0/22"}, {"vlan": "100", "mac": "9440.c942.96be", "type": "DYNAMIC", "ports": "Gi1/0/22"}, {"vlan": "100", "mac": "9457.a514.d52d", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "98e7.432f.fdd3", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9c7b.ef4c.6430", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9c7b.ef4c.6569", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9c7b.ef4c.656b", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9c7b.ef4c.6591", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9c7b.ef4d.2d2e", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9c7b.ef4d.2d2f", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9c7b.ef4d.2d62", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9c7b.ef4d.2d66", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "9cae.d3e2.c40c", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "a40e.7596.6d00", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "a45d.3664.0239", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "a478.0685.aad1", "type": "STATIC", "ports": "Vl100"}, {"vlan": "100", "mac": "ac91.a1bb.80f6", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "ac91.a1bb.8441", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "ac91.a1c3.0d15", "type": "DYNAMIC", "ports": "Gi2/0/20"}, {"vlan": "100", "mac": "b07b.2523.f799", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "b07b.2523.f8a6", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "b07b.2523.fb27", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "b07b.2523.fc81", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "b8be.bf2a.9bc1", "type": "DYNAMIC", "ports": "Gi1/0/22"}, {"vlan": "100", "mac": "bc45.5be9.a88c", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "bc4a.56f1.81d1", "type": "DYNAMIC", "ports": "Te1/1/7"}, {"vlan": "100", "mac": "c047.0e91.2e35", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c047.0e91.31db", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c047.0e91.31e9", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d20e.a515", "type": "DYNAMIC", "ports": "Gi1/0/24"}, {"vlan": "100", "mac": "c8d9.d20e.a8b5", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d20e.a8b9", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d20e.a8eb", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d20e.ad87", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d20e.faa5", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d20e.facb", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d212.c225", "type": "DYNAMIC", "ports": "Te1/1/7"}, {"vlan": "100", "mac": "c8d9.d212.c237", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d213.2586", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d22a.5eb3", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d22a.65b5", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d22a.79e4", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d22a.7eab", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d22a.7ec3", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d22a.a0ef", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d22a.a29c", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "c8d9.d22b.53cd", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "cc96.e551.bb44", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "cc96.e551.bb6d", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "cc96.e551.bbdb", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "cc96.e59f.3a9a", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d067.2683.5400", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0ad.08c1.67fe", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0ad.08c1.6800", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0ad.08c1.6802", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0ad.08c1.6809", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0ad.08c1.680b", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0ad.08c1.681a", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0bf.9ccf.21c0", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0bf.9ccf.40c0", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0bf.9ccf.f140", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0c2.4e41.0209", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0c2.4e41.020a", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0c2.4e41.020e", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0c2.4e41.020f", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0c2.4e41.0211", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0c2.4e41.0212", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0c2.4e41.0213", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d0c2.4e41.0214", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "d8c4.97d8.bdba", "type": "DYNAMIC", "ports": "Te1/1/7"}, {"vlan": "100", "mac": "dc4a.3eae.7ed5", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "e003.6bb8.953a", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "ecb1.d742.a730", "type": "DYNAMIC", "ports": "Gi1/0/24"}, {"vlan": "100", "mac": "f07f.06ee.c460", "type": "DYNAMIC", "ports": "Gi1/0/1"}, {"vlan": "100", "mac": "f4cf.e2ff.7c7d", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "f4cf.e2ff.7cb7", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "f4cf.e2ff.7cdb", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "f4cf.e2ff.7d35", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "f4cf.e2ff.7d4c", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "100", "mac": "f8a2.6df7.9d77", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "100", "mac": "fc3f.dbbc.782d", "type": "DYNAMIC", "ports": "Te1/1/7"}, {"vlan": "100", "mac": "fc3f.dbbc.7830", "type": "DYNAMIC", "ports": "Te2/1/6"}, {"vlan": "110", "mac": "001b.1700.0230", "type": "DYNAMIC", "ports": "Po4"}, {"vlan": "110", "mac": "a478.0685.aacd", "type": "STATIC", "ports": "Vl110"}, {"vlan": "160", "mac": "506b.8d80.a435", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d81.0d84", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d81.4595", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d81.7863", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d81.f934", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d83.2955", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d83.3e5e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d83.80e1", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d83.eb5f", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d85.ce48", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d85.ce5f", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d86.57c2", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d86.a3f2", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d88.8050", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d88.891d", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d88.e1ce", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8a.183d", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8a.9699", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8a.9ddf", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8a.a445", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8a.b469", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8c.e396", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8d.4849", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8d.e7bb", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8e.9e8c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8e.dc99", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d8f.e440", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d90.5c5c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d90.6397", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d90.fb94", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d91.22f2", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d91.6e7c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d91.fa17", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d93.59d1", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d94.6783", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d94.d7d3", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d95.3374", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d95.6b81", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d95.c6b1", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d96.26d8", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d96.d457", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d97.75a3", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d98.180f", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d98.535a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d98.7ca9", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d98.d7c0", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d98.ff9a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d99.1418", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d99.be79", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d9a.2225", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d9a.b582", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d9b.b36a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d9c.b418", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d9d.c451", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d9d.cbfd", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8d9f.18af", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da0.0905", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da0.3fac", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da0.80de", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da0.c8d1", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da0.fe58", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da2.1c8d", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da2.5bf3", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da2.eacd", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da3.0d7b", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da3.a59a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da3.d9ee", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da4.3f2f", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da4.811e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da5.36e0", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da5.3d92", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da6.2bf5", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da6.7a2b", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da6.948d", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da6.c4d0", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da7.0187", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da7.5343", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8da8.774d", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8daa.17c2", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dab.cf01", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dae.1205", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dae.1593", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dae.570f", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dae.daf7", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dae.df90", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db0.3da2", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db0.863a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db1.1a18", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db3.6da1", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db4.dd2f", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db4.e0a8", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db4.fe67", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db5.2897", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db5.a383", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db5.c965", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db6.989e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db6.e05c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db8.bb12", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db8.df16", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db8.fc50", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8db9.6e9d", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dbc.c5ef", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dbc.f8cc", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dbd.56af", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dbe.e26e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc0.ec8a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc1.5bca", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc4.11db", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc4.1470", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc4.173c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc5.25c0", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc5.aa54", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc5.b1c6", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc5.ed75", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc6.2bdb", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc6.4c7a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc6.5186", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc6.f3b2", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc7.2b15", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc7.354a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc7.e755", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc8.9487", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc8.9693", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc9.6106", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc9.6ccd", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc9.efde", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dc9.f3e0", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dcc.193c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dcc.43bd", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dcc.81fe", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dcd.a435", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dcd.bae8", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dce.2dd8", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dce.dd96", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dce.f6cc", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dcf.b7d7", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd0.27ab", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd0.74d9", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd2.03ac", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd2.bcb2", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd3.6799", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd3.7518", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd3.ca5c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd4.f864", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd6.d808", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd7.645f", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd7.b728", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd8.1023", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd8.9e20", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd9.0088", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dd9.c534", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dda.157e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dda.7e3e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dda.c946", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8ddb.e6fd", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8ddb.e94f", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8ddc.1566", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8ddd.0463", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8ddd.afbb", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8ddd.cc8c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8ddd.d9e1", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dde.30a2", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de1.3c69", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de1.9957", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de2.89ab", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de2.93b4", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de3.78ac", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de3.c55b", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de4.3320", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de5.69f0", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de5.eed0", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de6.944d", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de6.9b85", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de6.bf87", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de6.f59b", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de6.f655", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de7.b2e8", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de7.b469", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de7.cc90", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de8.3e3c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de8.ff7f", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de9.1636", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8de9.ed58", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dea.0dd7", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dea.74d5", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dea.d090", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dea.dae4", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8deb.57fa", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8deb.596e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8deb.8280", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8deb.b138", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8deb.dde2", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dec.1d49", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8ded.4462", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8ded.519b", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8def.4511", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df0.0b0b", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df0.abfd", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df0.c1fe", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df1.b060", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df2.45ee", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df2.9057", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df4.ce96", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df4.d9e4", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df5.bece", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df5.bede", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df6.49c7", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df6.e4c5", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8df8.f707", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfa.3d7c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfa.e685", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfb.5c93", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfb.7b86", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfb.8811", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfb.8dce", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfb.a352", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfc.4910", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfc.a004", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfc.b6dc", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfd.2748", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfd.3116", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfe.1059", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dfe.a694", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dff.4166", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "506b.8dff.5f6e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "160", "mac": "a478.0685.aadc", "type": "STATIC", "ports": "Vl160"}, {"vlan": "161", "mac": "08c0.eb45.c120", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "08c0.eb45.c12c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "08c0.eb45.c130", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "08c0.eb46.4f9e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "506b.8d78.3c61", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "506b.8d92.4d0c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "506b.8da3.e7b3", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "506b.8db3.98d5", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "506b.8dc7.22be", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "506b.8de4.6ed7", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "506b.8dea.2111", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "506b.8df7.eec8", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "506b.8dfb.8849", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "5254.0036.7580", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "5254.0047.5944", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "5254.00d7.5e7c", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "7cc2.55b3.0d1a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "7cc2.55b3.0e5a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "7cc2.55b3.1c9a", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "161", "mac": "a478.0685.aace", "type": "STATIC", "ports": "Vl161"}, {"vlan": "161", "mac": "e8eb.d309.d0ca", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "500", "mac": "001b.1700.0230", "type": "DYNAMIC", "ports": "Po4"}, {"vlan": "500", "mac": "00df.1d22.1b41", "type": "DYNAMIC", "ports": "Gi2/0/16"}, {"vlan": "500", "mac": "00df.1d22.59c1", "type": "DYNAMIC", "ports": "Gi1/0/16"}, {"vlan": "500", "mac": "3cec.ef82.efc9", "type": "STATIC", "ports": "Gi2/0/6"}, {"vlan": "500", "mac": "3cec.ef82.efce", "type": "STATIC", "ports": "Gi2/0/5"}, {"vlan": "500", "mac": "3cec.ef82.efd9", "type": "STATIC", "ports": "Gi1/0/6"}, {"vlan": "500", "mac": "3cec.ef82.efe5", "type": "STATIC", "ports": "Gi1/0/5"}, {"vlan": "500", "mac": "506b.8def.d95d", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "500", "mac": "506b.8dfc.783e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "500", "mac": "7cc2.5505.7616", "type": "STATIC", "ports": "Gi2/0/4"}, {"vlan": "500", "mac": "7cc2.5530.6d78", "type": "DYNAMIC", "ports": "Gi2/0/20"}, {"vlan": "500", "mac": "7cc2.5530.6e96", "type": "DYNAMIC", "ports": "Gi1/0/24"}, {"vlan": "500", "mac": "7cc2.5530.6f21", "type": "DYNAMIC", "ports": "Gi2/0/20"}, {"vlan": "500", "mac": "7cc2.5530.705f", "type": "DYNAMIC", "ports": "Gi1/0/24"}, {"vlan": "500", "mac": "8c36.7a01.c2b9", "type": "STATIC", "ports": "Gi2/0/2"}, {"vlan": "500", "mac": "8c36.7a01.c5f0", "type": "STATIC", "ports": "Gi1/0/4"}, {"vlan": "500", "mac": "a478.0685.aad0", "type": "STATIC", "ports": "Vl500"}, {"vlan": "500", "mac": "d0e0.42ad.ca27", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "500", "mac": "d0e0.42ad.ca77", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "513", "mac": "a478.0685.aaf0", "type": "STATIC", "ports": "Vl513"}, {"vlan": "450", "mac": "0027.e32f.04c4", "type": "DYNAMIC", "ports": "Gi1/0/18"}, {"vlan": "450", "mac": "0027.e32f.04c8", "type": "DYNAMIC", "ports": "Gi1/0/18"}, {"vlan": "450", "mac": "04d5.9005.3bc9", "type": "DYNAMIC", "ports": "Gi1/0/19"}, {"vlan": "450", "mac": "f07f.06ee.c461", "type": "DYNAMIC", "ports": "Gi1/0/20"}, {"vlan": "451", "mac": "04d5.9005.3bca", "type": "DYNAMIC", "ports": "Gi2/0/22"}, {"vlan": "451", "mac": "74a0.2fe0.7b21", "type": "STATIC", "ports": "Gi2/0/21"}, {"vlan": "451", "mac": "d44f.67ce.7a99", "type": "DYNAMIC", "ports": "Gi2/0/23"}, {"vlan": "509", "mac": "001b.1700.0230", "type": "DYNAMIC", "ports": "Po4"}, {"vlan": "509", "mac": "04d5.9005.3bd2", "type": "DYNAMIC", "ports": "Gi1/0/9"}, {"vlan": "509", "mac": "0c9d.9254.3878", "type": "DYNAMIC", "ports": "Gi1/0/3"}, {"vlan": "509", "mac": "506b.8dfc.783e", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "170", "mac": "7cc2.5522.b4c9", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "170", "mac": "7cc2.5523.1c99", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "170", "mac": "7cc2.553a.5471", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "170", "mac": "7cc2.553a.5731", "type": "DYNAMIC", "ports": "Po2"}, {"vlan": "170", "mac": "a478.0685.aac0", "type": "STATIC", "ports": "Vl170"}, {"vlan": "115", "mac": "001b.1700.0230", "type": "DYNAMIC", "ports": "Po4"}, {"vlan": "115", "mac": "085b.d6c6.d5c2", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "115", "mac": "2c6d.c16c.46ac", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "115", "mac": "2c6d.c16c.7861", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "115", "mac": "60a5.e23a.a705", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "115", "mac": "646c.808c.d027", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "115", "mac": "9010.5736.a173", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "115", "mac": "9010.5736.aaa6", "type": "DYNAMIC", "ports": "Gi2/0/17"}, {"vlan": "115", "mac": "a478.0685.aada", "type": "STATIC", "ports": "Vl115"}, {"vlan": "115", "mac": "b635.e63d.2e6d", "type": "DYNAMIC", "ports": "Gi2/0/17"}], "arp_table": [{"protocol": "Internet", "ip": "*********", "age": "0", "mac": "48ea.622d.c2a8", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "0", "mac": "d0ad.08c1.6802", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "7", "mac": "cc96.e59f.3a9a", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "78", "mac": "80e8.2cd7.e6e1", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********2", "age": "103", "mac": "04bf.1b0b.37f0", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********6", "age": "5", "mac": "c8d9.d22a.a29c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "c8d9.d22a.65b5", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********8", "age": "0", "mac": "80e8.2cec.3902", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "1860.24ae.deb8", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********6", "age": "65", "mac": "9c7b.ef4c.6569", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "2", "mac": "207b.d26a.e1e8", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "3", "mac": "80e8.2cec.290c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "80e8.2cec.33a6", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "b07b.2523.f8a6", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********00", "age": "0", "mac": "9c7b.ef4d.2d2e", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********01", "age": "1", "mac": "c8d9.d20e.ad87", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********36", "age": "0", "mac": "1860.248a.423e", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "1860.2487.9305", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "80e8.2cec.382d", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********82", "age": "0", "mac": "9c7b.ef4c.656b", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********94", "age": "130", "mac": "0068.ebaa.46e3", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********12", "age": "2", "mac": "b07b.2523.f799", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********16", "age": "0", "mac": "9c7b.ef4c.6591", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********4", "age": "1", "mac": "0068.ebae.ad22", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********32", "age": "0", "mac": "84a9.3e6c.7e2a", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********33", "age": "1", "mac": "9c7b.ef4d.2d66", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********50", "age": "0", "mac": "0068.eb9c.3b18", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "9", "mac": "9440.c942.96be", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "1", "mac": "9440.c942.768e", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********1", "age": "2", "mac": "0080.d409.f7b3", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********2", "age": "1", "mac": "0006.6743.d868", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********8", "age": "212", "mac": "d0c2.4e41.0212", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "167", "mac": "84a9.3e73.9a43", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "80e8.2cec.3810", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "1860.248a.423c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "5", "mac": "ecb1.d742.a730", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "84a9.3e6c.7870", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "c8d9.d22a.a0ef", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "84a9.3e6c.76df", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "c8d9.d22b.53cd", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "80e8.2cec.38df", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "0068.ebb6.56db", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********4", "age": "4", "mac": "b07b.2523.fb27", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********7", "age": "0", "mac": "c8d9.d22a.79e4", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********9", "age": "0", "mac": "c8d9.d20e.a8eb", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "18", "mac": "80e8.2cf6.c6db", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "80e8.2cd8.3f5a", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "4", "mac": "80e8.2cf6.c6e3", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "3", "mac": "80e8.2cf6.c6d7", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "0068.eba5.3fc8", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********03", "age": "0", "mac": "c8d9.d22a.7ec3", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********08", "age": "0", "mac": "c8d9.d22a.5eb3", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********22", "age": "0", "mac": "b07b.2523.fc81", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********35", "age": "0", "mac": "84a9.3e6c.7e27", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********43", "age": "0", "mac": "1860.2488.c9d5", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********49", "age": "0", "mac": "0068.eba5.3f5c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********57", "age": "0", "mac": "84a9.3e7e.e893", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********61", "age": "0", "mac": "1860.248a.4232", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********62", "age": "0", "mac": "001a.6e01.0642", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********85", "age": "0", "mac": "c8d9.d212.c225", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********86", "age": "0", "mac": "1860.2488.c9cd", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********90", "age": "0", "mac": "c8d9.d20e.a8b9", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********93", "age": "0", "mac": "84a9.3e63.9985", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********98", "age": "1", "mac": "0068.ebb6.55c0", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "7", "mac": "c8d9.d20e.facb", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "c8d9.d213.2586", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "c8d9.d20e.a515", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "c8d9.d212.c237", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "1", "mac": "80e8.2ce3.a6df", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "9c7b.ef4d.2d62", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "c8d9.d20e.a8b5", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "84a9.3e63.9781", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "c8d9.d22a.7eab", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "5", "mac": "0050.5693.e410", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "10", "mac": "0050.5693.0367", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "0", "mac": "0050.5693.3655", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********0", "age": "7", "mac": "0050.5693.e410", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********2", "age": "0", "mac": "0050.5693.6032", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********4", "age": "0", "mac": "0050.5693.d02f", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********7", "age": "7", "mac": "0050.5693.3dee", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********9", "age": "3", "mac": "0050.5693.8212", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********0", "age": "0", "mac": "0050.5693.6b0e", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********2", "age": "15", "mac": "0050.56a8.f2c7", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "0050.56a8.804f", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********7", "age": "3", "mac": "9440.c92f.a224", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********8", "age": "5", "mac": "0050.5693.6394", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********9", "age": "9", "mac": "9440.c92f.e254", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********0", "age": "0", "mac": "9009.d00c.b082", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********1", "age": "1", "mac": "60e8.5b0a.5978", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********4", "age": "3", "mac": "00c0.ff52.24b1", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********6", "age": "161", "mac": "0050.5693.0609", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********9", "age": "0", "mac": "00c0.ff52.1e7a", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "-", "mac": "a478.0685.aad1", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "0050.5693.653f", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "04d5.9005.3bc8", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "f07f.06ee.c460", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "2", "mac": "74a0.2fe0.7b20", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "001b.1700.0230", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "3822.e2d7.fa46", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "0017.c8dc.12d3", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "f8a2.6df7.9d77", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "a45d.3664.0239", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "9cae.d3e2.c40c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "2880.2310.2869", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "4", "mac": "a40e.7596.6d00", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "fc3f.dbbc.782d", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "0c97.5f2c.7dc0", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "fc3f.dbbc.7830", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "9457.a514.d52d", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "2829.8653.b643", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "3", "mac": "2829.864a.a436", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********01", "age": "0", "mac": "84f1.47fa.0528", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********02", "age": "2", "mac": "3863.bb67.20c0", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********03", "age": "1", "mac": "dc4a.3eae.7ed5", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********04", "age": "2", "mac": "5820.b1fa.6d80", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********05", "age": "4", "mac": "0c97.5f24.1240", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********09", "age": "0", "mac": "b8be.bf2a.9bc1", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********10", "age": "0", "mac": "5835.d9e4.5c41", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********11", "age": "3", "mac": "5835.d9b6.c7c1", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********12", "age": "0", "mac": "d8c4.97d8.bdba", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********13", "age": "5", "mac": "8480.2df9.6a20", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********14", "age": "0", "mac": "f4cf.e2ff.7cdb", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********15", "age": "0", "mac": "f4cf.e2ff.7c7d", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********16", "age": "0", "mac": "f4cf.e2ff.7d35", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********17", "age": "0", "mac": "64f6.9d5e.b16b", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********18", "age": "17", "mac": "64f6.9d5e.b200", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********19", "age": "17", "mac": "f4cf.e2ff.7cb7", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********20", "age": "1", "mac": "f4cf.e2ff.7d4c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********21", "age": "2", "mac": "3464.a9a2.4300", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********22", "age": "0", "mac": "0409.73b1.f200", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********23", "age": "5", "mac": "5820.b1f4.a200", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********24", "age": "6", "mac": "d0bf.9ccf.f140", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********25", "age": "6", "mac": "d0bf.9ccf.21c0", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********27", "age": "6", "mac": "d0bf.9ccf.40c0", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********29", "age": "9", "mac": "3863.bb5d.5380", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********30", "age": "6", "mac": "d067.2683.5400", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********31", "age": "11", "mac": "3863.bb55.9bc0", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********32", "age": "109", "mac": "bc4a.56f1.81d1", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********33", "age": "0", "mac": "445b.ed16.eb80", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********34", "age": "3", "mac": "0c97.5f24.e820", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********36", "age": "0", "mac": "0c97.5f24.6020", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********37", "age": "0", "mac": "0050.5693.5069", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********44", "age": "0", "mac": "6c3b.e516.b0fc", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********51", "age": "0", "mac": "84a9.3e63.9799", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********52", "age": "104", "mac": "d0c2.4e41.020a", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********53", "age": "3", "mac": "9c7b.ef4d.2d2f", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********54", "age": "4", "mac": "c047.0e91.31db", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********57", "age": "5", "mac": "80e8.2cec.38f3", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********62", "age": "1", "mac": "48ea.622b.2dcd", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********64", "age": "9", "mac": "4cea.4160.d794", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********66", "age": "0", "mac": "d0ad.08c1.67fe", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********67", "age": "104", "mac": "e003.6bb8.953a", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********72", "age": "104", "mac": "d0c2.4e41.0214", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********76", "age": "0", "mac": "d0ad.08c1.6809", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********77", "age": "1", "mac": "9c7b.ef4c.6430", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********78", "age": "5", "mac": "d0ad.08c1.681a", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********80", "age": "1", "mac": "d0ad.08c1.680b", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********92", "age": "0", "mac": "c8d9.d20e.faa5", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********96", "age": "0", "mac": "3473.5aa8.27e3", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********03", "age": "0", "mac": "80e8.2cec.381c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********06", "age": "1", "mac": "98e7.432f.fdd3", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********10", "age": "4", "mac": "c047.0e91.31e9", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********13", "age": "0", "mac": "80e8.2ce3.a639", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********15", "age": "3", "mac": "0068.ebb6.56d9", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********16", "age": "0", "mac": "d0ad.08c1.6800", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********1", "age": "0", "mac": "04bf.1b29.89f2", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********3", "age": "16", "mac": "cc96.e59f.3b2c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********7", "age": "0", "mac": "ac91.a1c3.0d15", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "8", "mac": "cc96.e551.bbdb", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "26", "mac": "0068.ebaa.46eb", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "4", "mac": "3814.28b0.d0b6", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********1", "age": "118", "mac": "0068.eba5.3e5e", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "ac91.a1bb.80f6", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "220", "mac": "d0c2.4e41.020f", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "182", "mac": "0068.ebae.ad43", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "104", "mac": "d0c2.4e41.0213", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "5", "mac": "d0c2.4e41.020e", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "1860.2487.9303", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "0068.ebaa.46e5", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "64c9.01a8.7626", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "104", "mac": "d0c2.4e41.0209", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "2", "mac": "cc96.e551.bb44", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "104", "mac": "d0c2.4e41.0211", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "26", "mac": "c047.0e91.3076", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "104", "mac": "bc45.5be9.a88c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "80e8.2cd7.e6fb", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "04bf.1b29.d51d", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "04bf.1b29.86d8", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "04bf.1b16.9d78", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "ac91.a1bb.8441", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "0c37.9638.ac5c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "**********", "age": "82", "mac": "04bf.1b62.c6c4", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "6", "mac": "04bf.1b62.8599", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "32", "mac": "c047.0e91.2e35", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "2800.af19.9d6d", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "7", "mac": "04bf.1b0b.37f2", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "0c37.9669.cb59", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "176", "mac": "04bf.1b62.c67c", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "***********", "age": "-", "mac": "a478.0685.aada", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "***********", "age": "18", "mac": "8480.2df9.6a24", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "***********", "age": "98", "mac": "d0ab.d590.d63e", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "***********", "age": "105", "mac": "1847.3d4b.6ba3", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "***********", "age": "18", "mac": "9010.5736.a173", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "60a5.e23a.a705", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "************", "age": "1", "mac": "646c.808c.d027", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "************", "age": "86", "mac": "9010.5736.aaa6", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "************", "age": "8", "mac": "2c6d.c16c.7861", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "************", "age": "94", "mac": "085b.d6c6.d5c2", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "b635.e63d.2e6d", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "2c6d.c16c.46ac", "type": "ARPA", "interface": "Vlan115"}, {"protocol": "Internet", "ip": "***********", "age": "-", "mac": "a478.0685.aacd", "type": "ARPA", "interface": "Vlan110"}, {"protocol": "Internet", "ip": "***********", "age": "35", "mac": "8480.2df9.6a24", "type": "ARPA", "interface": "Vlan110"}, {"protocol": "Internet", "ip": "**********", "age": "-", "mac": "a478.0685.aac0", "type": "ARPA", "interface": "Vlan170"}, {"protocol": "Internet", "ip": "**********", "age": "192", "mac": "7cc2.553a.5471", "type": "ARPA", "interface": "Vlan170"}, {"protocol": "Internet", "ip": "**********", "age": "119", "mac": "7cc2.553a.5731", "type": "ARPA", "interface": "Vlan170"}, {"protocol": "Internet", "ip": "**********", "age": "159", "mac": "7cc2.5523.1c99", "type": "ARPA", "interface": "Vlan170"}, {"protocol": "Internet", "ip": "**********", "age": "228", "mac": "7cc2.5522.b4c9", "type": "ARPA", "interface": "Vlan170"}, {"protocol": "Internet", "ip": "**********", "age": "3", "mac": "506b.8dc4.173c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********", "age": "0", "mac": "506b.8dc4.11db", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********", "age": "2", "mac": "506b.8dff.5f6e", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********", "age": "3", "mac": "506b.8d83.80e1", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********", "age": "2", "mac": "506b.8de6.bf87", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********", "age": "2", "mac": "506b.8de5.eed0", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********", "age": "2", "mac": "506b.8db9.6e9d", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********", "age": "1", "mac": "506b.8dc6.f3b2", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "4", "mac": "506b.8df0.abfd", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "2", "mac": "506b.8dfd.2748", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "1", "mac": "506b.8d8a.9ddf", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "506b.8dfa.3d7c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "506b.8dc7.354a", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "506b.8d99.1418", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "506b.8d85.ce48", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "506b.8dc9.6106", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "1", "mac": "506b.8d8a.b469", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "506b.8da6.c4d0", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "506b.8dd7.b728", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "506b.8d90.fb94", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "506b.8da4.811e", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "2", "mac": "506b.8dbe.e26e", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "4", "mac": "506b.8dd3.7518", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "506b.8db0.863a", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********6", "age": "0", "mac": "506b.8db1.1a18", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********7", "age": "1", "mac": "506b.8da0.80de", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********8", "age": "3", "mac": "506b.8df1.b060", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********9", "age": "3", "mac": "506b.8d98.535a", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********0", "age": "4", "mac": "506b.8dfc.a004", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********1", "age": "0", "mac": "506b.8d97.75a3", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********2", "age": "0", "mac": "506b.8dc5.aa54", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********3", "age": "1", "mac": "506b.8da8.774d", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********4", "age": "3", "mac": "506b.8dd4.f864", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********5", "age": "0", "mac": "506b.8d94.d7d3", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********6", "age": "4", "mac": "506b.8df2.45ee", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********7", "age": "1", "mac": "506b.8d90.5c5c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********8", "age": "4", "mac": "506b.8d85.ce5f", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********9", "age": "3", "mac": "506b.8d81.4595", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********0", "age": "1", "mac": "506b.8df4.ce96", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********1", "age": "4", "mac": "506b.8d98.180f", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********2", "age": "2", "mac": "506b.8de8.3e3c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********3", "age": "1", "mac": "506b.8de9.ed58", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********4", "age": "3", "mac": "506b.8dd3.ca5c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********5", "age": "0", "mac": "506b.8d8a.9699", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********6", "age": "0", "mac": "506b.8ddb.e6fd", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********7", "age": "2", "mac": "506b.8dbc.c5ef", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********8", "age": "3", "mac": "506b.8dc8.9487", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********9", "age": "0", "mac": "506b.8dc9.6ccd", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********0", "age": "1", "mac": "506b.8db5.2897", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********1", "age": "3", "mac": "506b.8d90.6397", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********2", "age": "3", "mac": "506b.8dbc.f8cc", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********3", "age": "0", "mac": "506b.8da2.eacd", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********4", "age": "0", "mac": "506b.8ddb.e94f", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********5", "age": "3", "mac": "506b.8d9d.c451", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********6", "age": "3", "mac": "506b.8ddd.d9e1", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********7", "age": "1", "mac": "506b.8dec.1d49", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********8", "age": "0", "mac": "506b.8dea.0dd7", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********9", "age": "1", "mac": "506b.8da6.7a2b", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********0", "age": "3", "mac": "506b.8dc6.2bdb", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********1", "age": "2", "mac": "506b.8d95.c6b1", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********2", "age": "1", "mac": "506b.8dc9.efde", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********3", "age": "1", "mac": "506b.8df0.0b0b", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********4", "age": "1", "mac": "506b.8dfb.8811", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********5", "age": "3", "mac": "506b.8dff.4166", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********6", "age": "4", "mac": "506b.8dc1.5bca", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********7", "age": "3", "mac": "506b.8d95.6b81", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********8", "age": "0", "mac": "506b.8dc5.b1c6", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********9", "age": "2", "mac": "506b.8db4.e0a8", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********0", "age": "3", "mac": "506b.8d98.ff9a", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********1", "age": "2", "mac": "506b.8d94.6783", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********2", "age": "3", "mac": "506b.8dcf.b7d7", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********3", "age": "3", "mac": "506b.8d81.f934", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********4", "age": "2", "mac": "506b.8dea.dae4", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********5", "age": "2", "mac": "506b.8de7.b469", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********6", "age": "3", "mac": "506b.8d80.a435", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********7", "age": "4", "mac": "506b.8d8a.183d", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********8", "age": "3", "mac": "506b.8de8.ff7f", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********9", "age": "2", "mac": "506b.8d99.be79", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********0", "age": "3", "mac": "506b.8da7.5343", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********1", "age": "3", "mac": "506b.8d93.59d1", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********2", "age": "1", "mac": "506b.8d95.3374", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********3", "age": "4", "mac": "506b.8deb.57fa", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********4", "age": "2", "mac": "506b.8de6.f59b", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********5", "age": "3", "mac": "506b.8df6.e4c5", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********6", "age": "0", "mac": "506b.8da6.948d", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********7", "age": "3", "mac": "506b.8da6.2bf5", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********8", "age": "3", "mac": "506b.8dea.d090", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********9", "age": "4", "mac": "506b.8df0.c1fe", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********0", "age": "3", "mac": "506b.8da3.0d7b", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********1", "age": "0", "mac": "506b.8da7.0187", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "1", "mac": "506b.8dda.c946", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "506b.8de2.89ab", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "506b.8deb.dde2", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "1", "mac": "506b.8dae.1593", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "1", "mac": "506b.8d98.d7c0", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "506b.8dc6.4c7a", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "506b.8dce.2dd8", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "506b.8db8.df16", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "1", "mac": "506b.8db5.c965", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "2", "mac": "506b.8dd0.74d9", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8dbd.56af", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8dd8.9e20", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8dc7.e755", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8d9d.cbfd", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8dc0.ec8a", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "4", "mac": "506b.8da4.3f2f", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "2", "mac": "506b.8d9a.2225", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8dc4.1470", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8dcc.193c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "4", "mac": "506b.8dc8.9693", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "2", "mac": "506b.8def.4511", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "1", "mac": "506b.8dfb.a352", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "4", "mac": "506b.8daa.17c2", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "1", "mac": "506b.8d8a.a445", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8deb.8280", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "2", "mac": "506b.8de9.1636", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8de3.c55b", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "2", "mac": "506b.8df8.f707", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8d8e.dc99", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8dfe.a694", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "5", "mac": "506b.8d88.8050", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "4", "mac": "506b.8dea.74d5", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "2", "mac": "506b.8df6.49c7", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "4", "mac": "506b.8da2.5bf3", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "2", "mac": "506b.8d83.eb5f", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8d8d.4849", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8d8c.e396", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8dcc.81fe", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8db4.fe67", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "4", "mac": "506b.8da5.3d92", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "2", "mac": "506b.8d88.e1ce", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "4", "mac": "506b.8d9f.18af", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "4", "mac": "506b.8d8e.9e8c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8db0.3da2", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "1", "mac": "506b.8d9b.b36a", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8ded.4462", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8dd2.bcb2", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8d8d.e7bb", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "2", "mac": "506b.8df4.d9e4", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8dd9.0088", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "2", "mac": "506b.8da3.d9ee", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "3", "mac": "506b.8dfe.1059", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********4", "age": "1", "mac": "506b.8dc7.2b15", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********5", "age": "0", "mac": "506b.8d8f.e440", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********6", "age": "0", "mac": "506b.8de3.78ac", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "3", "mac": "506b.8dae.daf7", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********8", "age": "2", "mac": "506b.8db8.fc50", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********9", "age": "3", "mac": "506b.8ddd.afbb", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********0", "age": "0", "mac": "506b.8dda.7e3e", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********1", "age": "0", "mac": "506b.8dcc.43bd", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********2", "age": "3", "mac": "506b.8d98.7ca9", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********3", "age": "2", "mac": "506b.8da0.0905", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********5", "age": "3", "mac": "506b.8d88.891d", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********6", "age": "2", "mac": "506b.8deb.b138", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "2", "mac": "506b.8db6.989e", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********8", "age": "2", "mac": "506b.8dd3.6799", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********9", "age": "0", "mac": "506b.8db8.bb12", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********1", "age": "4", "mac": "506b.8df5.bece", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********2", "age": "3", "mac": "506b.8dcd.a435", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********3", "age": "4", "mac": "506b.8dc5.25c0", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********4", "age": "1", "mac": "506b.8dc5.ed75", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********5", "age": "2", "mac": "506b.8de6.944d", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********6", "age": "0", "mac": "506b.8d91.6e7c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "0", "mac": "506b.8d86.a3f2", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********8", "age": "2", "mac": "506b.8dce.dd96", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********9", "age": "3", "mac": "506b.8dd0.27ab", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********1", "age": "3", "mac": "506b.8dfc.b6dc", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********2", "age": "2", "mac": "506b.8dcd.bae8", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********3", "age": "2", "mac": "506b.8dc9.f3e0", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********5", "age": "3", "mac": "506b.8d91.22f2", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********6", "age": "4", "mac": "506b.8dd7.645f", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "1", "mac": "506b.8de7.cc90", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********8", "age": "3", "mac": "506b.8d81.0d84", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********9", "age": "2", "mac": "506b.8dd2.03ac", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********1", "age": "0", "mac": "506b.8de1.3c69", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "2", "mac": "506b.8db5.a383", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "2", "mac": "506b.8ddc.1566", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********8", "age": "3", "mac": "506b.8dae.1205", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********9", "age": "1", "mac": "506b.8d86.57c2", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********0", "age": "2", "mac": "506b.8dd8.1023", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********1", "age": "1", "mac": "506b.8df2.9057", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********2", "age": "0", "mac": "506b.8da5.36e0", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********3", "age": "2", "mac": "506b.8da0.fe58", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********4", "age": "1", "mac": "506b.8dc6.5186", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********5", "age": "2", "mac": "506b.8de4.3320", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********6", "age": "2", "mac": "506b.8dae.570f", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "4", "mac": "506b.8ddd.cc8c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********9", "age": "4", "mac": "506b.8da3.a59a", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********0", "age": "2", "mac": "506b.8db6.e05c", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8d96.26d8", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********2", "age": "2", "mac": "506b.8da0.3fac", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********3", "age": "3", "mac": "506b.8da2.1c8d", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********4", "age": "0", "mac": "506b.8deb.596e", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********5", "age": "0", "mac": "506b.8dfa.e685", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********6", "age": "1", "mac": "506b.8dfb.5c93", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "5", "mac": "506b.8dd9.c534", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********8", "age": "1", "mac": "506b.8dd6.d808", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********9", "age": "3", "mac": "506b.8dda.157e", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********0", "age": "5", "mac": "506b.8df5.bede", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********1", "age": "1", "mac": "506b.8de2.93b4", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********3", "age": "2", "mac": "506b.8dfb.7b86", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********4", "age": "1", "mac": "506b.8d81.7863", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********5", "age": "0", "mac": "506b.8ddd.0463", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********6", "age": "4", "mac": "506b.8dab.cf01", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "1", "mac": "506b.8d9a.b582", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********8", "age": "0", "mac": "506b.8dde.30a2", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********9", "age": "1", "mac": "506b.8de6.f655", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********0", "age": "0", "mac": "506b.8d9c.b418", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********1", "age": "0", "mac": "506b.8d83.3e5e", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********2", "age": "0", "mac": "506b.8dfb.8dce", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********3", "age": "0", "mac": "506b.8d91.fa17", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********4", "age": "1", "mac": "506b.8d83.2955", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********5", "age": "3", "mac": "506b.8dfd.3116", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "1", "mac": "506b.8d96.d457", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********8", "age": "1", "mac": "506b.8da0.c8d1", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********9", "age": "0", "mac": "506b.8db3.6da1", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********0", "age": "1", "mac": "506b.8de5.69f0", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********1", "age": "2", "mac": "506b.8ded.519b", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********2", "age": "2", "mac": "506b.8de7.b2e8", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********4", "age": "0", "mac": "506b.8de1.9957", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********5", "age": "4", "mac": "506b.8dae.df90", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********6", "age": "1", "mac": "506b.8dfc.4910", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********7", "age": "2", "mac": "506b.8dce.f6cc", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********8", "age": "3", "mac": "506b.8db4.dd2f", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********2", "age": "3", "mac": "506b.8de6.9b85", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "***********", "age": "-", "mac": "a478.0685.aadc", "type": "ARPA", "interface": "Vlan160"}, {"protocol": "Internet", "ip": "**********", "age": "-", "mac": "a478.0685.aace", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********0", "age": "126", "mac": "5254.0036.7580", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********1", "age": "0", "mac": "08c0.eb45.c130", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********2", "age": "0", "mac": "08c0.eb46.4f9e", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********3", "age": "0", "mac": "08c0.eb45.c120", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********4", "age": "0", "mac": "08c0.eb45.c12c", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********5", "age": "0", "mac": "e8eb.d309.d0ca", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "7cc2.55b3.1c9a", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "7cc2.55b3.0e5a", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "7cc2.55b3.0d1a", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "2", "mac": "506b.8dea.2111", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "72", "mac": "506b.8dc7.22be", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "506b.8d78.3c61", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "506b.8da3.e7b3", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "107", "mac": "5254.0036.7580", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "17", "mac": "5254.00d7.5e7c", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "1", "mac": "5254.0047.5944", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********00", "age": "236", "mac": "5254.0047.5944", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********01", "age": "158", "mac": "506b.8de4.6ed7", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********02", "age": "5", "mac": "506b.8d92.4d0c", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********03", "age": "7", "mac": "506b.8df7.eec8", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********04", "age": "0", "mac": "506b.8de4.6ed7", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "**********05", "age": "0", "mac": "506b.8dfb.8849", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********2", "age": "11", "mac": "506b.8db3.98d5", "type": "ARPA", "interface": "Vlan161"}, {"protocol": "Internet", "ip": "***********", "age": "-", "mac": "a478.0685.aad0", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "001b.1700.0230", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********", "age": "40", "mac": "d0e0.42ad.ca77", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********", "age": "3", "mac": "d0e0.42ad.ca27", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********", "age": "0", "mac": "506b.8dfc.783e", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********", "age": "6", "mac": "506b.8def.d95d", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********", "age": "8", "mac": "00df.1d22.59c1", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "************", "age": "195", "mac": "00df.1d22.1b41", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********5", "age": "86", "mac": "8c36.7a01.c5f0", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********6", "age": "46", "mac": "8c36.7a01.c2b9", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********1", "age": "8", "mac": "3cec.ef82.efd9", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********2", "age": "244", "mac": "3cec.ef82.efe5", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********3", "age": "1", "mac": "3cec.ef82.efc9", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********4", "age": "2", "mac": "3cec.ef82.efce", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "***********5", "age": "1", "mac": "7cc2.5505.7616", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "************", "age": "252", "mac": "7cc2.5589.0e19", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "************", "age": "252", "mac": "7cc2.5589.0e2c", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "************", "age": "252", "mac": "7cc2.5589.0e1b", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "*************", "age": "11", "mac": "7cc2.5530.705f", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "*************", "age": "3", "mac": "7cc2.5530.6e96", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "*************", "age": "13", "mac": "7cc2.5530.6f21", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "*************", "age": "1", "mac": "7cc2.5530.6d78", "type": "ARPA", "interface": "Vlan500"}, {"protocol": "Internet", "ip": "************", "age": "0", "mac": "506b.8dfc.783e", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "11", "mac": "506b.8d8a.5b85", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "14", "mac": "506b.8ddb.3c70", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "0", "mac": "506b.8ddd.362a", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "191", "mac": "506b.8d87.2602", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "1", "mac": "506b.8dc1.5769", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "1", "mac": "506b.8d8a.2ca4", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "1", "mac": "506b.8dd1.d9d4", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "241", "mac": "506b.8dde.1b51", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "144", "mac": "506b.8df6.cac8", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "*************", "age": "0", "mac": "506b.8d8a.0636", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "************", "age": "-", "mac": "a478.0685.aad4", "type": "ARPA", "interface": "Vlan11"}, {"protocol": "Internet", "ip": "***************", "age": "4", "mac": "cc96.e551.bb6d", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*************", "age": "146", "mac": "00c0.ff52.1e7a", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*************", "age": "242", "mac": "00c0.ff52.24b1", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "3", "mac": "0050.5665.d0ac", "type": "ARPA", "interface": "Vlan100"}, {"protocol": "Internet", "ip": "*********", "age": "5", "mac": "0050.5660.7f8c", "type": "ARPA", "interface": "Vlan100"}], "interface_descriptions": {"Port-channel2": "* Trunk Nx183-1 & -2 *", "Port-channel3": "* PA183-1 LAN eth3-4 *", "Port-channel4": "* PA183-2 LAN eth3-4 *", "Gi1/0/1": "* Ro183-1 Vodafone **", "Gi1/0/2": "* trunk sniffer port12 *", "Gi1/0/3": "* CAMERA ASUS ROUTER *", "Gi1/0/4": "* PA183-1 MGT *", "Gi1/0/5": "* bras-nu1a mgmt *", "Gi1/0/6": "* bras-nu1b mgmt *", "Gi1/0/7": "* TS183-1 <PERSON><PERSON> *", "Gi1/0/8": "* bras-nu2a *", "Gi1/0/9": "* FW183 DMZ port11 *", "Gi1/0/10": "* NAS *", "Gi1/0/11": "* Fortigate port 1 (lan) *", "Gi1/0/12": "* vm2a vmnic1 traffic *", "Gi1/0/13": "* PA183-1 LAN eth3 *", "Gi1/0/14": "* vm2a mgt *", "Gi1/0/15": "* SAN MSA2050 mgmt *", "Gi1/0/16": "* Trk to sw183-ISP1 *", "Gi1/0/17": "*connected to FW183 DMZ Port 9 *", "Gi1/0/18": "* Romtelecom - Router *", "Gi1/0/19": "* Romtelecom - FG port 2 *", "Gi1/0/20": "* Romtelecom - Ro183-1 *", "Gi1/0/21": "* Data *", "Gi1/0/22": "* Trk to sw183-3 *", "Gi1/0/23": "* PA183-1 LAN eth4 *", "Gi1/0/24": "* Trk to sw183-1 *", "Te1/1/1": "* Trk vm2a nic5 *", "Te1/1/2": "* Trk vm2b nic4 *", "Te1/1/7": "* Trk to sw183-4 *", "Te1/1/8": "* Trk to Nx183-1 *", "Gi2/0/1": "* Ro183-2 *", "Gi2/0/2": "* PA183-2 MGT *", "Gi2/0/3": "* PA183-2 LAN eth3 *", "Gi2/0/4": "* bras-nu1e mgmt *", "Gi2/0/5": "* bras-nu1c mgmt *", "Gi2/0/6": "* bras-nu1d mgmt *", "Gi2/0/7": "* Data *", "Gi2/0/8": "* Data *", "Gi2/0/9": "* vm2b mgt *", "Gi2/0/10": "* PA183-2 LAN eth4 *", "Gi2/0/11": "***connected to Brasov-vm2b *****", "Gi2/0/12": "* bras-nu2c *", "Gi2/0/13": "* Data *", "Gi2/0/14": "* vm2a nic2 *", "Gi2/0/15": "* vm2b vmnic1 traffic *", "Gi2/0/16": "* Trk to sw183-ISP2 *", "Gi2/0/17": "* Brasov-wlc1 *", "Gi2/0/18": "* bras-nu2b *", "Gi2/0/19": "* Data *", "Gi2/0/20": "* Trk to sw183-2 *", "Gi2/0/21": "* Vodafone Ro183-2 port G0/1 *", "Gi2/0/22": "* Vodafone FG port 3 *", "Gi2/0/23": "* Vodafone router *", "Gi2/0/24": "* Trunk to sw182-1 *", "Te2/1/1": "* Trk vm2b nic5 *", "Te2/1/2": "* Trk vm2a nic4 *", "Te2/1/6": "* Sw183-5 - HP 5400 B8 *", "Te2/1/7": "* Sw183-5 - HP 5400 B8 *", "Te2/1/8": "* Trk to Nx183-1 *", "Vl110": "* Visitors *", "Vl160": "* VDI Nutanix *", "Vl161": "* Srvrs Nutanix *", "Vl170": "* <PERSON><PERSON><PERSON> *", "Vl500": "* MGM vlan 500 *"}, "port_configurations": {"Port-channel2": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Port-channel3": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Port-channel4": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi0/0": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/1": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/2": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "100,110,451,509", "native_vlan": ""}, "Gi1/0/3": {"mode": "access", "access_vlan": "509", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/4": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/5": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/6": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/7": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/8": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/9": {"mode": "access", "access_vlan": "509", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/10": {"mode": "access", "access_vlan": "513", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/11": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/12": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/13": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/14": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/15": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/16": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "500", "native_vlan": ""}, "Gi1/0/17": {"mode": "access", "access_vlan": "509", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/18": {"mode": "access", "access_vlan": "450", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/19": {"mode": "access", "access_vlan": "450", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/20": {"mode": "access", "access_vlan": "450", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/21": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/22": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/23": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/0/24": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/1/1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/1/2": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/1/3": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi1/1/4": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te1/1/1": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te1/1/2": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te1/1/3": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te1/1/4": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te1/1/5": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te1/1/6": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te1/1/7": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te1/1/8": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Fo1/1/1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Fo1/1/2": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "TwentyFiveGigE1/1/1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "TwentyFiveGigE1/1/2": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "AppGigabitEthernet1/0/1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/1": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/2": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/3": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/4": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/5": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/6": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/7": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/8": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/9": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/10": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/11": {"mode": "access", "access_vlan": "509", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/12": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/13": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/14": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/15": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/16": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "500", "native_vlan": ""}, "Gi2/0/17": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "100,110,115", "native_vlan": ""}, "Gi2/0/18": {"mode": "access", "access_vlan": "500", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/19": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/20": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/21": {"mode": "access", "access_vlan": "451", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/22": {"mode": "access", "access_vlan": "451", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/23": {"mode": "access", "access_vlan": "451", "allowed_vlans": "", "native_vlan": ""}, "Gi2/0/24": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi2/1/1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi2/1/2": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi2/1/3": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Gi2/1/4": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te2/1/1": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te2/1/2": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te2/1/3": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te2/1/4": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te2/1/5": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Te2/1/6": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Te2/1/7": {"mode": "access", "access_vlan": "100", "allowed_vlans": "", "native_vlan": ""}, "Te2/1/8": {"mode": "trunk", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Fo2/1/1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Fo2/1/2": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "TwentyFiveGigE2/1/1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "TwentyFiveGigE2/1/2": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "AppGigabitEthernet2/0/1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl1": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl11": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl100": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl110": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl115": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl160": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl161": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl170": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl500": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}, "Vl513": {"mode": "access", "access_vlan": "1", "allowed_vlans": "", "native_vlan": ""}}, "interface_status": "\nPort         Name               Status       Vlan       Duplex  Speed Type\nGi1/0/1      * Ro183-1 Vodafone connected    100        a-full a-1000 10/100/1000BaseTX\nGi1/0/2      * trunk sniffer po disabled     trunk        auto   auto 10/100/1000BaseTX\nGi1/0/3      * CAMERA ASUS ROUT connected    509        a-full a-1000 10/100/1000BaseTX\nGi1/0/4      * PA183-1 MGT *    connected    500        a-full a-1000 10/100/1000BaseTX\nGi1/0/5      * bras-nu1a mgmt * connected    500        a-full a-1000 10/100/1000BaseTX\nGi1/0/6      * bras-nu1b mgmt * connected    500        a-full a-1000 10/100/1000BaseTX\nGi1/0/7      * TS183-1 Perle *  connected    100        a-full a-1000 10/100/1000BaseTX\nGi1/0/8      * bras-nu2a *      connected    500        a-full a-1000 10/100/1000BaseTX\nGi1/0/9      * FW183 DMZ port11 connected    509        a-full a-1000 10/100/1000BaseTX\nGi1/0/10     * NAS *            notconnect   513          auto   auto 10/100/1000BaseTX\nGi1/0/11     * Fortigate port 1 connected    100        a-full a-1000 10/100/1000BaseTX\nGi1/0/12     * vm2a vmnic1 traf disabled     100          auto   auto 10/100/1000BaseTX\nGi1/0/13     * PA183-1 LAN eth3 connected    trunk      a-full a-1000 10/100/1000BaseTX\nGi1/0/14     * vm2a mgt *       connected    100        a-full a-1000 10/100/1000BaseTX\nGi1/0/15     * SAN MSA2050 mgmt connected    100        a-full a-1000 10/100/1000BaseTX\nGi1/0/16     * Trk to sw183-ISP connected    trunk      a-full a-1000 10/100/1000BaseTX\nGi1/0/17     *connected to FW18 connected    509        a-full a-1000 10/100/1000BaseTX\nGi1/0/18     * Romtelecom - Rou connected    450        a-full a-1000 10/100/1000BaseTX\nGi1/0/19     * Romtelecom - FG  connected    450        a-full a-1000 10/100/1000BaseTX\nGi1/0/20     * Romtelecom - Ro1 connected    450        a-full a-1000 10/100/1000BaseTX\nGi1/0/21     * Data *           notconnect   100          auto   auto 10/100/1000BaseTX\nGi1/0/22     * Trk to sw183-3 * connected    trunk      a-full a-1000 10/100/1000BaseTX\nGi1/0/23     * PA183-1 LAN eth4 connected    trunk      a-full a-1000 10/100/1000BaseTX\nGi1/0/24     * Trk to sw183-1 * connected    trunk      a-full a-1000 10/100/1000BaseTX\nTe1/1/1      * Trk vm2a nic5 *  connected    trunk        full    10G SFP-10GBase-CX1\nTe1/1/2      * Trk vm2b nic4 *  connected    trunk        full    10G SFP-10GBase-CX1\nTe1/1/3                         notconnect   1            auto   auto unknown\nTe1/1/4                         notconnect   1            auto   auto unknown\nTe1/1/5                         notconnect   1            auto   auto unknown\nTe1/1/6                         notconnect   1            auto   auto unknown\nTe1/1/7      * Trk to sw183-4 * connected    trunk        full    10G SFP-10GBase-CX1\nTe1/1/8      * Trk to Nx183-1 * connected    trunk        full    10G SFP-10GBase-CX1\nAp1/0/1                         connected    1          a-full a-1000 App-hosting port\nGi2/0/1      * Ro183-2 *        connected    100        a-full a-1000 10/100/1000BaseTX\nGi2/0/2      * PA183-2 MGT *    connected    500        a-full a-1000 10/100/1000BaseTX\nGi2/0/3      * PA183-2 LAN eth3 connected    trunk      a-full a-1000 10/100/1000BaseTX\nGi2/0/4      * bras-nu1e mgmt * connected    500        a-full a-1000 10/100/1000BaseTX\nGi2/0/5      * bras-nu1c mgmt * connected    500        a-full a-1000 10/100/1000BaseTX\nGi2/0/6      * bras-nu1d mgmt * connected    500        a-full a-1000 10/100/1000BaseTX\nGi2/0/7      * Data *           notconnect   100          auto   auto 10/100/1000BaseTX\nGi2/0/8      * Data *           notconnect   100          auto   auto 10/100/1000BaseTX\nGi2/0/9      * vm2b mgt *       connected    100        a-full a-1000 10/100/1000BaseTX\nGi2/0/10     * PA183-2 LAN eth4 connected    trunk      a-full a-1000 10/100/1000BaseTX\nGi2/0/11     ***connected to Br notconnect   509          auto   auto 10/100/1000BaseTX\nGi2/0/12     * bras-nu2c *      connected    500        a-full a-1000 10/100/1000BaseTX\nGi2/0/13     * Data *           connected    100        a-full a-1000 10/100/1000BaseTX\nGi2/0/14     * vm2a nic2 *      connected    100        a-full a-1000 10/100/1000BaseTX\nGi2/0/15     * vm2b vmnic1 traf disabled     100          auto   auto 10/100/1000BaseTX\nGi2/0/16     * Trk to sw183-ISP connected    trunk      a-full a-1000 10/100/1000BaseTX\nGi2/0/17     * Brasov-wlc1 *    connected    trunk      a-full a-1000 10/100/1000BaseTX\nGi2/0/18     * bras-nu2b *      connected    500        a-full a-1000 10/100/1000BaseTX\nGi2/0/19     * Data *           notconnect   100          auto   auto 10/100/1000BaseTX\nGi2/0/20     * Trk to sw183-2 * connected    trunk      a-full a-1000 10/100/1000BaseTX\nGi2/0/21     * Vodafone Ro183-2 connected    451        a-full a-1000 10/100/1000BaseTX\nGi2/0/22     * Vodafone FG port connected    451        a-full a-1000 10/100/1000BaseTX\nGi2/0/23     * Vodafone router  connected    451        a-full a-1000 10/100/1000BaseTX\nGi2/0/24     * Trunk to sw182-1 notconnect   trunk        auto   auto 10/100/1000BaseTX\nTe2/1/1      * Trk vm2b nic5 *  connected    trunk        full    10G SFP-10GBase-CX1\nTe2/1/2      * Trk vm2a nic4 *  connected    trunk        full    10G SFP-10GBase-CX1\nTe2/1/3                         notconnect   1            auto   auto unknown\nTe2/1/4                         notconnect   1            auto   auto unknown\nTe2/1/5                         notconnect   1            auto   auto unknown\nTe2/1/6      * Sw183-5 - HP 540 connected    100          full    10G SFP-10GBase-SR\nTe2/1/7      * Sw183-5 - HP 540 notconnect   100          full    10G SFP-10GBase-SR\nTe2/1/8      * Trk to Nx183-1 * connected    trunk        full    10G SFP-10GBase-CX1\nAp2/0/1                         connected    1          a-full a-1000 App-hosting port\nPo2          * Trunk Nx183-1 &  connected    trunk      a-full  a-10G N/A\nPo3          * PA183-1 LAN eth3 connected    trunk      a-full a-1000 N/A\nPo4          * PA183-2 LAN eth3 connected    trunk      a-full a-1000 N/A", "running_config": "Building configuration...\n\nCurrent configuration : 40316 bytes\n!\n! Last configuration change at 16:01:19 EDT Tue May 13 2025 by admin\n! NVRAM config last updated at 16:01:04 EDT Tue May 13 2025 by admin\n!\nversion 16.12\nno service pad\nservice timestamps debug datetime msec\nservice timestamps log datetime msec\nservice password-encryption\nservice call-home\nservice unsupported-transceiver\nplatform punt-keepalive disable-kernel-core\n!\nhostname sw183-0\n!\n!\nvrf definition Mgmt-vrf\n !\n address-family ipv4\n exit-address-family\n !\n address-family ipv6\n exit-address-family\n!\nenable password 7 08325B471D4156\n!\naaa new-model\n!\n!\n!\n!\n!\n!\n!\n!\naaa session-id common\nclock timezone EDT -5 0\nclock summer-time EDT recurring\nswitch 1 provision c9300-24t\nswitch 2 provision c9300-24t\n!\n!\n!\n!\ncall-home\n ! If contact email address in call-home is <NAME_EMAIL>\n ! the email address configured in Cisco Smart License Portal will be used as contact email address to send SCH notifications.\n contact-email-addr <EMAIL>\n profile \"CiscoTAC-1\"\n  active\n  destination transport-method http\n  no destination transport-method email\nip routing\n!\n!\n!\n!\n!\nip domain name canamgroup.canamdc.ws\n!\n!\n!\nip dhcp snooping vlan 100,160\nno ip dhcp snooping information option\nlogin on-success log\n!\n!\n!\n!\n!\n!\n!\nno device-tracking logging theft\n!\ntable-map AutoQos-4.0-Trust-Cos-Table\n default copy\n!\n!\ncrypto pki trustpoint SLA-TrustPoint\n enrollment terminal\n revocation-check crl\n!\ncrypto pki trustpoint TP-self-signed-*********\n enrollment selfsigned\n subject-name cn=IOS-Self-Signed-Certificate-*********\n revocation-check none\n rsakeypair TP-self-signed-*********\n!\n!\ncrypto pki certificate chain SLA-TrustPoint\n certificate ca 01\n  ******** ******** ******** ******** 300D0609 2A864886 F70D0101 0B050030 \n  32310E30 0C060355 040A1305 ******** 6F312030 1E060355 ******** ******** \n  6F204C69 63656E73 696E6720 526F6F74 ******** 1E170D31 ******** ******** \n  3834375A 170D3338 ******** ******** 34375A30 32310E30 0C060355 040A1305 \n  ******** 6F312030 1E060355 ******** ******** 6F204C69 63656E73 696E6720 \n  526F6F74 ******** 82012230 0D06092A 864886F7 0D010101 05000382 010F0030 \n  82010A02 82010100 A6BCBD96 131E05F7 145EA72C 2CD686E6 17222EA1 F1EFF64D \n  CBB4C798 212AA147 C655D8D7 9471380D 8711441E 1AAF071A 9CAE6388 8A38E520 \n  1C394D78 462EF239 C659F715 B98C0A59 5BBB5CBD 0CFEBEA3 700A8BF7 D8F256EE \n  4AA4E80D DB6FD1C9 60B1FD18 FFC69C96 6FA68957 A2617DE7 104FDC5F EA2956AC \n  7390A3EB 2B5436AD C847A2C5 DAB553EB 69A9A535 58E9F3E3 C0BD23CF 58BD7188 \n  68E69491 20F320E7 948E71D7 AE3BCC84 F10684C7 4BC8E00F 539BA42B 42C68BB7 \n  C7479096 B4CB2D62 EA2F505D C7B062A4 6811D95B E8250FC4 5D5D5FB8 8F27D191 \n  C55F0D76 61F9A4CD 3D992327 A8BB03BD 4E6D7069 7CBADF8B DF5F4368 95135E44 \n  DFC7C6CF 04DD7FD1 02030100 01A34230 40300E06 03551D0F 0101FF04 04030201 \n  06300F06 03551D13 0101FF04 05300301 01FF301D 0603551D 0E041604 1449DC85 \n  4B3D31E5 1B3E6A17 606AF333 3D3B4C73 E8300D06 092A8648 86F70D01 010B0500 \n  03820101 00507F24 D3932A66 86025D9F E838AE5C 6D4DF6B0 49631C78 240DA905 \n  604EDCDE FF4FED2B 77FC460E CD636FDB DD44681E 3A5673AB 9093D3B1 6C9E3D8B \n  D98987BF E40CBD9E 1AECA0C2 2189BB5C 8FA85686 CD98B646 5575B146 8DFC66A8 \n  467A3DF4 4D565700 6ADF0F0D CF835015 3C04FF7C 21E878AC 11BA9CD2 55A9232C \n  7CA7B7E6 C1AF74F6 152E99B7 B1FCF9BB E973DE7F 5BDDEB86 C71E3B49 1765308B \n  5FB0DA06 B92AFE7F 494E8A9E 07B85737 F3A58BE1 1A48A229 C37C1E69 39F08678 \n  80DDCD16 D6BACECA EEBC7CF9 8428787B 35202CDC 60E4616A B623CDBD 230E3AFB \n  418616A9 4093E049 4D10AB75 27E86F73 932E35B5 8862FDAE 0275156F 719BB2F0 \n  D697DF7F 28\n  \tquit\ncrypto pki certificate chain TP-self-signed-*********\n certificate self-signed 01\n  3082032E 30820216 ******** ******** 300D0609 2A864886 F70D0101 05050030 \n  30312E30 2C060355 04031325 494F532D 53656C66 2D536967 6E65642D 43657274 \n  69666963 6174652D 38313039 37353530 36301E17 0D323131 31323931 36313435 \n  325A170D 33303031 30313030 30303030 5A303031 2E302C06 03550403 1325494F \n  532D5365 6C662D53 69676E65 642D4365 72746966 69636174 652D3831 30393735 \n  35303630 82012230 0D06092A 864886F7 0D010101 05000382 010F0030 82010A02 \n  82010100 BD8BADA7 0546E6B7 35E485DC 00100FBC 332AA7E8 8C1F7771 895CD444 \n  9E1CBD06 227922E4 808D8CDD BE77789E 258D8142 979EC026 5CD90390 4DE73843 \n  2C01983B 6161ED6D F7F0F63F 42560205 BEB063A5 E347497F BE50DB99 60FA2C37 \n  A159CF20 85307CD9 25A6FC74 4599D9FC E20E6088 5ED910DF 8155E651 7E6A77FE \n  CD942646 430E67F8 CCA9967E 468AB7B0 20D98EA1 BBCE11D6 2DCC49A3 3C362A5B \n  EB4F051C 0922EF27 7A390494 89A5C065 006E6643 70B784F4 A9622232 25244F8A \n  E2AB6682 AD97D584 EBA4641B 9F89A3FF 3F896FCD 9A9CCDF7 15C6DD4F 886D2E2C \n  BDB2B86B D4BF80F2 0E2DD7F8 9FC55E75 E9E9E055 072A68EF 524C2C7B 42BD02ED \n  34E0D7F7 02030100 01A35330 51300F06 03551D13 0101FF04 05300301 01FF301F \n  0603551D 23041830 16801400 FC481834 2ED3DE95 C7894F31 C3DE1D18 90995C30 \n  1D060355 1D0E0416 041400FC 4818342E D3DE95C7 894F31C3 DE1D1890 995C300D \n  06092A86 4886F70D 01010505 00038201 01007F6A AC8651B9 76F60594 A486FFA6 \n  41AB5B57 D2A51525 0C5AD8EB 6803D666 943BEFB7 E700E382 F4C86F48 77D27CDC \n  8C030CBD 36288DDB BE6A0607 7B934C18 20364939 F24EDD04 C5B02B09 B0729CC5 \n  DF291EC1 E4BA7C22 2B541B4B 27798A1E E15C77A6 8D5840B1 A09F5F56 223FE83C \n  20807B03 D753BE1E 0DE5ED84 BBCBF8B1 CDCAAE09 D1BAF2E0 7B10FB34 6ADAF0CF \n  7F3A68FE 2F3EDD5D 848186D2 B439DDD5 CD18296D 8EF24710 FBB993B6 F42B9771 \n  45015CFC 159714C1 134F71E3 7A283F6D 68F7D3D8 2B681658 B5E32C6E CDB685E6 \n  4C477623 F7A1D6AC 56914113 AB9A047F 0E8863AF 82530089 C313A9AD 32951E07 \n  550A6E25 5E1FE99D 4070F254 DF337CE7 3487\n  \tquit\n!\ncrypto pki certificate pool\n cabundle nvram:ios_core.p7b\n!\n!\nlicense boot level network-advantage addon dna-advantage\n!\nmacro name VOICE\ndescription * Voice et Data *\nswitchport mode access\nswitchport nonegotiate\nswitchport access vlan 100\nswitchport voice vlan 301\nswitchport port-security\nswitchport port-security maximum 3\nswitchport port-security violation restrict\nswitchport port-security aging time 2\nswitchport port-security aging type inactivity\nspanning-tree guard root\nswitchport priority extend cos 0\nauto qos voip cisco-phone\nmls qos trust dscp\nmls qos trust device cisco-phone\nstorm-control broadcast level 1.00\nip dhcp snooping limit rate 5\nno shutdown\n@\nmacro name NO\nno description\nno switchport access vlan 100\nno switchport trunk encapsulation dot1q\nswitchport mode access\nno switchport trunk allowed vlan 100,300-302\nno switchport nonegotiate\nno switchport port-security\nno switchport port-security aging time 2\nno switchport port-security violation restrict\nno switchport port-security aging type inactivity\nno srr-queue bandwidth share 10 10 60 20\nno srr-queue bandwidth shape  10  0  0  0\nno queue-set 2\nno mls qos trust dscp\nno mls qos trust device cisco-phone\nno macro description\nno auto qos voip trust\nno storm-control broadcast level 1.00\nno spanning-tree portfast disable\nno spanning-tree bpdufilter disable\nno spanning-tree bpduguard disable\nno spanning-tree guard root\nno ip dhcp snooping limit rate 5\nno ip dhcp snooping trust\n@\nmacro name APS\nswitchport access vlan 300\nswitchport mode access\nswitchport nonegotiate\nsrr-queue bandwidth share 10 10 60 20\nsrr-queue bandwidth shape  10  0  0  0\nqueue-set 2\nmls qos trust device cisco-phone\nmls qos trust dscp\nauto qos voip cisco-phone\nstorm-control broadcast level 1.00\nspanning-tree portfast\nspanning-tree guard root\nip dhcp snooping limit rate 5\nno shutdown\n@\nmacro name TRUNK\ndescription * Trunk *\nswitchport trunk encapsulation dot1q\nswitchport trunk allowed vlan all\nswitchport mode trunk\nswitchport nonegotiate\nsrr-queue bandwidth share 10 10 60 20\nsrr-queue bandwidth shape  10  0  0  0\nqueue-set 2\nauto qos voip trust\nspanning-tree portfast disable\nspanning-tree bpdufilter disabl\nspanning-tree bpduguard disable\nspanning-tree guard root\nip dhcp snooping trust\nmls qos trust dscp\nno shutdown\n@\nmacro name DATA\n description * Data *\n switchport mode access\n switchport nonegotiate\n switchport access vlan  100\n switchport port-security\n switchport port-security maximum 1\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n spanning-tree guard root\n mls qos trust dscp\n storm-control broadcast level 1.00\n ip dhcp snooping limit rate 5\n no shutdown\n@\n!\ndiagnostic bootup level minimal\n!\nspanning-tree mode pvst\nspanning-tree loopguard default\nspanning-tree portfast default\nspanning-tree portfast bpduguard default\nspanning-tree extend system-id\nspanning-tree uplinkfast\nspanning-tree backbonefast\nno spanning-tree vlan 450\nspanning-tree vlan 11,100,110,160-161,170,500,509,513 priority 28672\narchive\n path ftp://***********/$h$t.cfg\n write-memory\n time-period 1440\nmemory free low-watermark processor 134335\n!\nusername admin secret 9 $9$q5noofdYbZe4iE$ekybh3j4m6NDkHGFRqOwqc/zNZK9grtOR4IPQUVHL/o\n!\nredundancy\n mode sso\n!\n!\n!\n!\n!\ntransceiver type all\n monitoring\n!\n!\nclass-map match-any system-cpp-police-ewlc-control\n  description EWLC Control \nclass-map match-any AutoQos-4.0-Output-Multimedia-Conf-Queue\n match dscp af41  af42  af43 \n match cos  4 \nclass-map match-any system-cpp-police-topology-control\n  description Topology control\nclass-map match-any system-cpp-police-sw-forward\n  description Sw forwarding, L2 LVX data packets, LOGGING, Transit Traffic\nclass-map match-any AutoQos-4.0-Output-Bulk-Data-Queue\n match dscp af11  af12  af13 \n match cos  1 \nclass-map match-any system-cpp-default\n  description EWLC Data, Inter FED Traffic \nclass-map match-any system-cpp-police-sys-data\n  description Openflow, Exception, EGR Exception, NFL Sampled Data, RPF Failed\nclass-map match-any AutoQos-4.0-Output-Priority-Queue\n match dscp cs4  cs5  ef \n match cos  5 \nclass-map match-any system-cpp-police-punt-webauth\n  description Punt Webauth\nclass-map match-any AutoQos-4.0-Output-Multimedia-Strm-Queue\n match dscp af31  af32  af33 \nclass-map match-any system-cpp-police-l2lvx-control\n  description L2 LVX control packets\nclass-map match-any system-cpp-police-forus\n  description Forus Address resolution and Forus traffic\nclass-map match-any system-cpp-police-multicast-end-station\n  description MCAST END STATION\nclass-map match-any system-cpp-police-high-rate-app\n  description High Rate Applications \nclass-map match-any system-cpp-police-multicast\n  description MCAST Data\nclass-map match-any system-cpp-police-l2-control\n  description L2 control\nclass-map match-any system-cpp-police-dot1x-auth\n  description DOT1X Auth\nclass-map match-any system-cpp-police-data\n  description ICMP redirect, ICMP_GEN and BROADCAST\nclass-map match-all AutoQoS-VoIP-RTP-Trust\n match ip dscp ef \nclass-map match-any system-cpp-police-stackwise-virt-control\n  description Stackwise Virtual OOB\nclass-map match-all AutoQoS-VoIP-Control-Trust\n match ip dscp cs3  af31 \nclass-map match-any non-client-nrt-class\nclass-map match-any system-cpp-police-routing-control\n  description Routing control and Low Latency\nclass-map match-any system-cpp-police-protocol-snooping\n  description Protocol snooping\nclass-map match-any AutoQos-4.0-Output-Trans-Data-Queue\n match dscp af21  af22  af23 \n match cos  2 \nclass-map match-any system-cpp-police-dhcp-snooping\n  description DHCP snooping\nclass-map match-any system-cpp-police-ios-routing\n  description L2 control, Topology control, Routing control, Low Latency\nclass-map match-any system-cpp-police-system-critical\n  description System Critical and Gold Pkt\nclass-map match-any AutoQos-4.0-Output-Scavenger-Queue\n match dscp cs1 \nclass-map match-any system-cpp-police-ios-feature\n  description ICMPGEN,BROADCAST,ICMP,L2LVXCntrl,ProtoSnoop,PuntWebauth,MCASTData,Transit,DOT1XAuth,Swfwd,LOGGING,L2LVXData,ForusTraffic,ForusARP,McastEndStn,Openflow,Exception,EGRExcption,NflSampled,RpfFailed\nclass-map match-any AutoQos-4.0-Output-Control-Mgmt-Queue\n match dscp cs2  cs3  cs6  cs7 \n match cos  3 \n!\npolicy-map AutoQos-4.0-Output-Policy\n class AutoQos-4.0-Output-Priority-Queue\n  priority level 1 percent 30\n class AutoQos-4.0-Output-Control-Mgmt-Queue\n  bandwidth remaining percent 10 \n  queue-limit dscp cs2 percent 80\n  queue-limit dscp cs3 percent 90\n  queue-limit dscp cs6 percent 100\n  queue-limit dscp cs7 percent 100\n  queue-buffers ratio 10\n class AutoQos-4.0-Output-Multimedia-Conf-Queue\n  bandwidth remaining percent 10 \n  queue-buffers ratio 10\n class AutoQos-4.0-Output-Trans-Data-Queue\n  bandwidth remaining percent 10 \n  queue-buffers ratio 10\n class AutoQos-4.0-Output-Bulk-Data-Queue\n  bandwidth remaining percent 4 \n  queue-buffers ratio 10\n class AutoQos-4.0-Output-Scavenger-Queue\n  bandwidth remaining percent 1 \n  queue-buffers ratio 10\n class AutoQos-4.0-Output-Multimedia-Strm-Queue\n  bandwidth remaining percent 10 \n  queue-buffers ratio 10\n class class-default\n  bandwidth remaining percent 25 \n  queue-buffers ratio 25\npolicy-map AutoQos-4.0-Trust-Cos-Input-Policy\n class class-default\n  set cos cos table AutoQos-4.0-Trust-Cos-Table\npolicy-map system-cpp-policy\npolicy-map AutoQoS-Police-CiscoPhone\n class AutoQoS-VoIP-RTP-Trust\n  set dscp ef\n class AutoQoS-VoIP-Control-Trust\n  set dscp cs3\n!\n! \n!\n!\n!\n!\n!\n!\n!\n!\n!\ninterface Port-channel2\n description * Trunk Nx183-1 & -2 *\n switchport mode trunk\n!\ninterface Port-channel3\n description * PA183-1 LAN eth3-4 *\n switchport mode trunk\n switchport nonegotiate\n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n ip dhcp snooping trust\n!\ninterface Port-channel4\n description * PA183-2 LAN eth3-4 *\n switchport mode trunk\n switchport nonegotiate\n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n ip dhcp snooping trust\n!\ninterface GigabitEthernet0/0\n vrf forwarding Mgmt-vrf\n no ip address\n shutdown\n negotiation auto\n!\ninterface GigabitEthernet1/0/1\n description * Ro183-1 Vodafone **\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/2\n description * trunk sniffer port12 *\n switchport trunk allowed vlan 100,110,451,509\n switchport mode trunk\n switchport nonegotiate\n shutdown\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface GigabitEthernet1/0/3\n description * CAMERA ASUS ROUTER *\n switchport access vlan 509\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/4\n description * PA183-1 MGT *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/5\n description * bras-nu1a mgmt *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/6\n description * bras-nu1b mgmt *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/7\n description * TS183-1 Perle *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/8\n description * bras-nu2a *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/9\n description * FW183 DMZ port11 *\n switchport access vlan 509\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/10\n description * NAS *\n switchport access vlan 513\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/11\n description * Fortigate port 1 (lan) *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/12\n description * vm2a vmnic1 traffic *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n shutdown\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/13\n description * PA183-1 LAN eth3 *\n switchport mode trunk\n switchport nonegotiate\n macro description TRUNK\n channel-group 3 mode active\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n ip dhcp snooping trust\n!\ninterface GigabitEthernet1/0/14\n description * vm2a mgt *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/15\n description * SAN MSA2050 mgmt *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/16\n description * Trk to sw183-ISP1 *\n switchport trunk allowed vlan 500\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface GigabitEthernet1/0/17\n description *connected to FW183 DMZ Port 9 *\n switchport access vlan 509\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/18\n description * Romtelecom - Router *\n switchport access vlan 450\n switchport mode access\n switchport nonegotiate\n no cdp enable\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree bpduguard disable\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/19\n description * Romtelecom - FG port 2 *\n switchport access vlan 450\n switchport mode access\n switchport nonegotiate\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/20\n description * Romtelecom - Ro183-1 *\n switchport access vlan 450\n switchport mode access\n switchport nonegotiate\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/21\n description * Data *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree bpduguard disable\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet1/0/22\n description * Trk to sw183-3 *\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface GigabitEthernet1/0/23\n description * PA183-1 LAN eth4 *\n switchport mode trunk\n switchport nonegotiate\n macro description TRUNK\n channel-group 3 mode active\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n ip dhcp snooping trust\n!\ninterface GigabitEthernet1/0/24\n description * Trk to sw183-1 *\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface GigabitEthernet1/1/1\n!\ninterface GigabitEthernet1/1/2\n!\ninterface GigabitEthernet1/1/3\n!\ninterface GigabitEthernet1/1/4\n!\ninterface TenGigabitEthernet1/1/1\n description * Trk vm2a nic5 *\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface TenGigabitEthernet1/1/2\n description * Trk vm2b nic4 *\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface TenGigabitEthernet1/1/3\n!\ninterface TenGigabitEthernet1/1/4\n!\ninterface TenGigabitEthernet1/1/5\n!\ninterface TenGigabitEthernet1/1/6\n!\ninterface TenGigabitEthernet1/1/7\n description * Trk to sw183-4 *\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface TenGigabitEthernet1/1/8\n description * Trk to Nx183-1 *\n switchport mode trunk\n channel-group 2 mode active\n!\ninterface FortyGigabitEthernet1/1/1\n!\ninterface FortyGigabitEthernet1/1/2\n!\ninterface TwentyFiveGigE1/1/1\n!\ninterface TwentyFiveGigE1/1/2\n!\ninterface AppGigabitEthernet1/0/1\n!\ninterface GigabitEthernet2/0/1\n description * Ro183-2 *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/2\n description * PA183-2 MGT *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/3\n description * PA183-2 LAN eth3 *\n switchport mode trunk\n switchport nonegotiate\n macro description TRUNK\n channel-group 4 mode active\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n ip dhcp snooping trust\n!\ninterface GigabitEthernet2/0/4\n description * bras-nu1e mgmt *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/5\n description * bras-nu1c mgmt *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/6\n description * bras-nu1d mgmt *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/7\n description * Data *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/8\n description * Data *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/9\n description * vm2b mgt *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/10\n description * PA183-2 LAN eth4 *\n switchport mode trunk\n switchport nonegotiate\n macro description TRUNK\n channel-group 4 mode active\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n ip dhcp snooping trust\n!\ninterface GigabitEthernet2/0/11\n description ***connected to Brasov-vm2b *****\n switchport access vlan 509\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/12\n description * bras-nu2c *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/13\n description * Data *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/14\n description * vm2a nic2 *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/15\n description * vm2b vmnic1 traffic *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security maximum 100\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n shutdown\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/16\n description * Trk to sw183-ISP2 *\n switchport trunk allowed vlan 500\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface GigabitEthernet2/0/17\n description * Brasov-wlc1 *\n switchport trunk allowed vlan 100,110,115\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface GigabitEthernet2/0/18\n description * bras-nu2b *\n switchport access vlan 500\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/19\n description * Data *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/20\n description * Trk to sw183-2 *\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface GigabitEthernet2/0/21\n description * Vodafone Ro183-2 port G0/1 *\n switchport access vlan 451\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n switchport port-security\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/22\n description * Vodafone FG port 3 *\n switchport access vlan 451\n switchport mode access\n switchport nonegotiate\n switchport port-security violation restrict\n switchport port-security aging time 2\n switchport port-security aging type inactivity\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree guard root\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/23\n description * Vodafone router *\n switchport access vlan 451\n switchport mode access\n switchport nonegotiate\n no cdp enable\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree portfast\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n ip dhcp snooping limit rate 5\n!\ninterface GigabitEthernet2/0/24\n description * Trunk to sw182-1 *\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard root\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface GigabitEthernet2/1/1\n!\ninterface GigabitEthernet2/1/2\n!\ninterface GigabitEthernet2/1/3\n!\ninterface GigabitEthernet2/1/4\n!\ninterface TenGigabitEthernet2/1/1\n description * Trk vm2b nic5 *\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface TenGigabitEthernet2/1/2\n description * Trk vm2a nic4 *\n switchport mode trunk\n switchport nonegotiate\n auto qos trust \n macro description TRUNK\n spanning-tree portfast disable\n spanning-tree bpdufilter disable\n spanning-tree bpduguard disable\n spanning-tree guard none\n service-policy input AutoQos-4.0-Trust-Cos-Input-Policy\n service-policy output AutoQos-4.0-Output-Policy\n ip dhcp snooping trust\n!\ninterface TenGigabitEthernet2/1/3\n!\ninterface TenGigabitEthernet2/1/4\n!\ninterface TenGigabitEthernet2/1/5\n!\ninterface TenGigabitEthernet2/1/6\n description * Sw183-5 - HP 5400 B8 *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree bpduguard disable\n spanning-tree guard none\n ip dhcp snooping limit rate 5\n!\ninterface TenGigabitEthernet2/1/7\n description * Sw183-5 - HP 5400 B8 *\n switchport access vlan 100\n switchport mode access\n switchport nonegotiate\n storm-control broadcast level 1.00\n macro description DATA\n spanning-tree bpduguard disable\n spanning-tree guard none\n ip dhcp snooping limit rate 5\n!\ninterface TenGigabitEthernet2/1/8\n description * Trk to Nx183-1 *\n switchport mode trunk\n channel-group 2 mode active\n!\ninterface FortyGigabitEthernet2/1/1\n!\ninterface FortyGigabitEthernet2/1/2\n!\ninterface TwentyFiveGigE2/1/1\n!\ninterface TwentyFiveGigE2/1/2\n!\ninterface AppGigabitEthernet2/0/1\n!\ninterface Vlan1\n no ip address\n shutdown\n!\ninterface Vlan11\n ip address ************ *************\n!\ninterface Vlan100\n ip address ********** *************\n ip helper-address *********\n ip helper-address *************\n!\ninterface Vlan110\n description * Visitors *\n ip address *********** *************\n ip helper-address *********\n ip helper-address *************\n!\ninterface Vlan115\n ip address *********** *************\n ip helper-address *********\n ip helper-address *************\n!\ninterface Vlan160\n description * VDI Nutanix *\n ip address *********** *************\n ip helper-address *********\n ip helper-address *************\n!\ninterface Vlan161\n description * Srvrs Nutanix *\n ip address ********** *************\n ip helper-address *********\n ip helper-address *************\n!\ninterface Vlan170\n description * Rubrik *\n ip address ********** ***************\n!\ninterface Vlan500\n description * MGM vlan 500 *\n ip address *********** *************\n ip helper-address *********8\n ip helper-address *********\n!\ninterface Vlan513\n no ip address\n!\n!\nrouter eigrp 100\n distribute-list prefix LOCAL out Vlan500\n distribute-list prefix LOCAL out Vlan100\n network ********* *********\n network ********* *********\n network ********** *********\n network ********** *********\n network ********** ********\n network ********** *********\n network ********** *********\n network *********** *********\n network *********** *********\n eigrp stub connected summary\n!\nrouter bgp 65185\n bgp log-neighbor-changes\n network ********* mask *************\n redistribute eigrp 100 metric 200 route-map EIGRP2BGP\n neighbor ********** remote-as 65183\n neighbor ********** description Fw183\n!\nip forward-protocol nd\nno ip http server\nno ip http secure-server\nip ftp username userbk\nip ftp password 7 04590E0A03245F710A180B1417191F572D2A362D\nip route 0.0.0.0 0.0.0.0 **********\nip ssh time-out 100\nip ssh authentication-retries 4\nip ssh version 2\n!\n!\n!\n!\nip prefix-list EIGRP2BGP seq 5 permit 10.0.0.0/8\n!\nip prefix-list LOCAL seq 5 permit **********/24\nip prefix-list LOCAL seq 10 permit ***********/24\nip prefix-list LOCAL seq 15 permit **********/22\nip prefix-list LOCAL seq 20 permit **********/24\nip prefix-list LOCAL seq 25 permit ***********/22\nip prefix-list LOCAL seq 30 permit **********/28\nip prefix-list LOCAL seq 35 permit *********/22\nip access-list standard 10\n 10 deny   any\nip access-list standard 20\n 10 permit **********\n 20 permit **********\n!\nroute-map EIGRP2BGP permit 5 \n match ip address prefix-list EIGRP2BGP\n!\nroute-map BGP2EIGRP permit 5 \n!\n!\nsnmp-server group GrSNMPv2 v2c \nsnmp-server group GrSNMPv3 v3 priv read DEFAULT \nsnmp-server view DEFAULT iso included\nsnmp-server community sGCMs RO\nsnmp-server community public RO\nsnmp-server location Brasov\nsnmp-server enable traps snmp authentication linkdown linkup coldstart warmstart\nsnmp-server enable traps eigrp\nsnmp-server enable traps vlancreate\nsnmp-server enable traps vlandelete\nsnmp-server enable traps port-security\nsnmp-server enable traps license\nsnmp-server enable traps cpu threshold\nsnmp-server enable traps memory bufferpeak\nsnmp-server enable traps stackwise\nsnmp-server enable traps flash insertion removal lowspace\nsnmp-server enable traps envmon\nsnmp-server enable traps event-manager\nsnmp-server enable traps hsrp\nsnmp-server enable traps errdisable\nsnmp-server enable traps transceiver all\nsnmp-server enable traps mac-notification change move threshold\nsnmp-server host *********** public \n!\n!\n!\ncontrol-plane\n service-policy input system-cpp-policy\n!\n!\nline con 0\n stopbits 1\nline vty 0 4\n exec-timeout 30 0\n length 25\n transport input ssh\nline vty 5 15\n transport input ssh\n!\nntp server **********\nntp server **********\nntp server **********\n!\n!\n!\n!\n!\n!\nend\n"}}, "networks": ["***********/24", "*********/22", "***********/22", "**********/24", "**********/24", "*********/22", "**********/22", "**********/28"], "ping_results": {"***********/24": ["***********", "***********", "***********", "***********", "***********", "***********", "***********5", "***********6", "************", "***********5", "*************", "*************", "*************", "*************"], "*********/22": ["*********", "*********", "*********", "*********6", "**********", "*********8", "**********", "*********6", "**********", "**********", "**********", "**********", "*********00", "*********01", "*********36", "***********", "***********", "*********82", "*********12", "*********16", "**********4", "*********33", "*********32", "*********50", "*********", "*********", "**********", "**********", "*********4", "*********8", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "*********2", "*********7", "*********9", "**********", "**********", "*********03", "**********", "*********35", "*********08", "**********", "*********49", "*********22", "*********57", "*********85", "*********61", "*********62", "*********86", "*********90", "*********93", "*********98", "***********", "***********", "***********", "***********", "***********", "***********", "*********4", "*********7", "*********6", "***********", "***********", "*********2", "*********0", "*********8", "*********9", "*********0", "*********", "*********9", "**********", "*********1", "*********", "*********", "*********2", "*********9", "**********", "*********0", "**********", "*********4", "**********", "**********", "*********7", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "*********16", "*********04", "*********03", "*********13", "*********12", "**********", "*********17", "*********01", "*********20", "**********", "*********27", "*********19", "*********23", "*********18", "*********30", "*********76", "*********31", "*********80", "*********44", "*********52", "*********67", "*********10", "*********25", "*********37", "*********15", "*********72", "*********53", "*********51", "*********24", "*********14", "*********77", "*********02", "*********11", "*********22", "**********", "*********29", "*********32", "*********62", "*********78", "*********66", "*********09", "*********96", "*********92", "*********03", "*********16", "*********06", "*********13", "**********1", "**********7", "*********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "**********", "***********"], "**********/28": ["**********", "**********", "**********", "**********", "**********"], "**********/22": ["**********", "**********", "**********", "**********", "**********", "**********", "**********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "**********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "**********7", "**********6", "**********0", "**********3", "**********9", "**********8", "**********1", "**********5", "**********7", "**********6", "**********4", "**********0", "**********4", "**********2", "**********0", "**********5", "**********3", "**********1", "**********9", "**********5", "**********9", "**********2", "**********4", "**********5", "**********6", "**********2", "**********1", "**********3", "**********7", "**********8", "**********7", "**********4", "**********9", "**********3", "**********6", "**********1", "**********2", "**********8", "**********0", "**********4", "**********3", "**********0", "**********1", "**********7", "**********8", "**********6", "**********9", "**********4", "**********8", "**********9", "**********5", "**********0", "**********1", "***********", "**********7", "**********2", "**********9", "**********7", "***********", "**********0", "***********", "**********1", "************", "**********6", "***********", "**********8", "***********", "**********2", "**********5", "***********", "************", "***********", "**********3", "***********", "**********6", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "***********7", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "************", "***********0", "************", "************", "************", "***********9", "***********3", "***********5", "************", "***********8", "***********8", "***********1", "***********2", "************", "***********4", "***********3", "***********2", "************", "***********8", "***********4", "***********5", "***********6", "************", "***********7", "***********7", "***********9", "***********3", "***********1", "***********6", "***********2", "***********1", "***********8", "***********7", "***********6", "***********2", "***********5", "***********7", "***********8", "***********7", "***********6", "***********5", "***********3", "***********6", "***********1", "***********1", "***********0", "***********5", "***********7", "***********9", "***********4", "***********9", "***********7", "***********9", "***********2", "***********4", "***********7", "***********8", "***********6", "***********5", "***********"], "**********/24": ["**********", "**********3", "**********0", "**********5", "**********1", "**********4", "**********2", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "***********", "**********00", "***********2"]}}