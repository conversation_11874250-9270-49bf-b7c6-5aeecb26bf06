#!/usr/bin/env python3
"""
Simple script to dump the running config from the switch
"""

from netmiko import Connect<PERSON><PERSON><PERSON>
from switch_parser import parse_switch_list


def dump_running_config():
    """Dump the running config to see what we're working with"""
    
    # Load switch info
    switches = parse_switch_list("switch_list.txt")
    if not switches:
        print("No switches found")
        return
    
    switch = switches[0]  # Use first switch
    
    device = {
        'device_type': switch.device_type,
        'host': switch.ip,
        'username': switch.username,
        'password': switch.password,
        'secret': switch.enable_password,
        'timeout': 30,
    }
    
    try:
        with ConnectHandler(**device) as connection:
            print(f"Connected to {switch.name}")

            # Check current privilege level
            prompt = connection.find_prompt()
            print(f"Current prompt: {prompt}")

            # Try to enter enable mode if not already there
            if not prompt.endswith('#'):
                print("Entering enable mode...")
                connection.enable()
                prompt = connection.find_prompt()
                print(f"New prompt: {prompt}")

            # Try different variations of the show running command
            commands_to_try = [
                "show running-config",
                "show run",
                "show configuration",
                "show config"
            ]

            show_run = None
            for cmd in commands_to_try:
                try:
                    print(f"\nTrying command: '{cmd}'...")
                    output = connection.send_command(cmd)
                    if "Invalid input" not in output and "% " not in output:
                        print(f"Success with '{cmd}'!")
                        show_run = output
                        break
                    else:
                        print(f"Failed: {output[:100]}...")
                except Exception as e:
                    print(f"Exception with '{cmd}': {e}")

            if not show_run:
                print("Could not retrieve running configuration with any command")

                # Try to get interface-specific config
                print("\nTrying to get interface Vlan500 config directly...")
                try:
                    vlan_config = connection.send_command("show running-config interface Vlan500")
                    print(f"Interface Vlan500 config: {vlan_config}")
                except Exception as e:
                    print(f"Failed to get interface config: {e}")

                return

            print(f"Running config length: {len(show_run)} characters")

            # Save to file
            with open("running_config_dump.txt", "w") as f:
                f.write(show_run)
            print("Running config saved to: running_config_dump.txt")

            # Show first 50 lines
            lines = show_run.split('\n')
            print(f"\nFirst 50 lines of running config ({len(lines)} total lines):")
            print("-" * 60)
            for i, line in enumerate(lines[:50]):
                print(f"{i+1:3}: {line}")

            if len(lines) > 50:
                print("... (truncated)")

            # Look for interface lines specifically
            print(f"\nSearching for lines containing 'interface':")
            interface_lines = []
            for i, line in enumerate(lines):
                if 'interface' in line.lower():
                    interface_lines.append((i+1, line))

            print(f"Found {len(interface_lines)} lines containing 'interface':")
            for line_num, line in interface_lines[:10]:  # Show first 10
                print(f"  Line {line_num}: {line}")

            if len(interface_lines) > 10:
                print(f"  ... and {len(interface_lines) - 10} more")
                
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    dump_running_config()
