#!/usr/bin/env python3
"""
Device Identifier Module
Uses NMAP to scan devices and identify what they are
"""

import subprocess
import json
import re
import concurrent.futures
from typing import Dict, List, Optional
import time


def run_nmap_scan(ip: str, timeout: int = 10) -> Dict[str, str]:
    """
    Run NMAP scan on a single IP address
    
    Args:
        ip: IP address to scan
        timeout: Timeout in seconds
        
    Returns:
        Dictionary with device information
    """
    device_info = {
        'ip': ip,
        'device_type': 'Unknown',
        'os': '',
        'services': '',
        'vendor': '',
        'hostname': ''
    }
    
    try:
        # Run NMAP with quick scan - fast service detection only
        cmd = [
            'nmap', '-sS', '-sV', '--version-intensity', '0',
            '--max-retries', '1', '--host-timeout', f'{timeout}s',
            '-T5', '--top-ports', '100', ip
        ]
        
        print(f"    Scanning {ip} with NMAP...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 10)
        
        if result.returncode == 0:
            output = result.stdout
            device_info.update(parse_nmap_output(output, ip))
        else:
            print(f"    NMAP scan failed for {ip}: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print(f"    NMAP scan timeout for {ip}")
    except FileNotFoundError:
        print(f"    NMAP not found. Please install nmap.")
        device_info['device_type'] = 'NMAP not available'
    except Exception as e:
        print(f"    Error scanning {ip}: {e}")
    
    return device_info


def parse_nmap_output(nmap_output: str, ip: str) -> Dict[str, str]:
    """
    Parse NMAP output to extract device information
    
    Args:
        nmap_output: Raw NMAP output
        ip: IP address being scanned
        
    Returns:
        Dictionary with parsed information
    """
    info = {
        'device_type': 'Unknown',
        'os': '',
        'services': '',
        'vendor': '',
        'hostname': ''
    }
    
    lines = nmap_output.split('\n')
    
    # Extract hostname
    hostname_match = re.search(r'Nmap scan report for (.+?) \(' + re.escape(ip) + r'\)', nmap_output)
    if hostname_match:
        info['hostname'] = hostname_match.group(1)
    
    # Extract OS information
    os_lines = []
    in_os_section = False
    for line in lines:
        if 'OS details:' in line or 'Running:' in line:
            in_os_section = True
            os_lines.append(line.strip())
        elif in_os_section and line.strip() and not line.startswith('Network Distance:'):
            if line.startswith('OS CPE:') or line.startswith('Aggressive OS'):
                break
            os_lines.append(line.strip())
        elif in_os_section and not line.strip():
            break
    
    if os_lines:
        info['os'] = ' '.join(os_lines)
    
    # Extract services
    services = []
    for line in lines:
        # Look for open ports with service info
        service_match = re.match(r'(\d+)/tcp\s+open\s+(.+)', line.strip())
        if service_match:
            port, service = service_match.groups()
            services.append(f"{port}:{service.strip()}")
    
    if services:
        info['services'] = '; '.join(services[:5])  # Limit to first 5 services
    
    # Extract MAC vendor if available
    vendor_match = re.search(r'MAC Address: [0-9A-F:]{17} \((.+?)\)', nmap_output)
    if vendor_match:
        info['vendor'] = vendor_match.group(1)
    
    # Determine device type based on services and OS
    info['device_type'] = determine_device_type(info['services'], info['os'], info['hostname'])
    
    return info


def determine_device_type(services: str, os_info: str, hostname: str) -> str:
    """
    Determine device type based on services, OS, and hostname
    
    Args:
        services: Service information
        os_info: OS information
        hostname: Hostname
        
    Returns:
        Device type string
    """
    services_lower = services.lower()
    os_lower = os_info.lower()
    hostname_lower = hostname.lower()
    
    # Network infrastructure
    if any(keyword in services_lower for keyword in ['snmp', 'ssh', 'telnet']) and \
       any(keyword in os_lower for keyword in ['cisco', 'juniper', 'hp', 'arista', 'switch', 'router']):
        return 'Network Device'
    
    # Printers
    if any(keyword in services_lower for keyword in ['ipp', 'printer', 'cups', '631', '9100']):
        return 'Printer'
    
    # Web servers
    if any(keyword in services_lower for keyword in ['http', 'https', 'apache', 'nginx', 'iis']):
        if any(keyword in services_lower for keyword in ['ssh', 'ftp']):
            return 'Server'
        else:
            return 'Web Server/Device'
    
    # Windows machines
    if any(keyword in services_lower for keyword in ['microsoft-ds', 'netbios', 'smb', '445', '139']):
        return 'Windows Computer'
    
    # Linux/Unix machines
    if 'ssh' in services_lower and any(keyword in os_lower for keyword in ['linux', 'unix']):
        return 'Linux/Unix Server'
    
    # Database servers
    if any(keyword in services_lower for keyword in ['mysql', 'postgresql', 'mssql', 'oracle']):
        return 'Database Server'
    
    # VoIP devices
    if any(keyword in services_lower for keyword in ['sip', '5060', 'voip']):
        return 'VoIP Device'
    
    # Security cameras/NVR
    if any(keyword in services_lower for keyword in ['rtsp', '554', 'onvif']) or \
       any(keyword in hostname_lower for keyword in ['camera', 'nvr', 'dvr']):
        return 'Security Camera/NVR'
    
    # IoT devices
    if any(keyword in hostname_lower for keyword in ['iot', 'sensor', 'thermostat', 'smart']):
        return 'IoT Device'
    
    # Mobile devices
    if any(keyword in os_lower for keyword in ['android', 'ios', 'mobile']):
        return 'Mobile Device'
    
    # Default based on open services
    if services:
        return 'Network Device'
    else:
        return 'Unknown'


def scan_responding_hosts(responding_hosts: List[str], max_workers: int = 10) -> Dict[str, Dict[str, str]]:
    """
    Scan all responding hosts with NMAP
    
    Args:
        responding_hosts: List of IP addresses that responded to ping
        max_workers: Maximum number of concurrent scans
        
    Returns:
        Dictionary mapping IP addresses to device information
    """
    device_info = {}
    
    if not responding_hosts:
        return device_info
    
    print(f"\nStarting NMAP scans of {len(responding_hosts)} responding hosts...")
    print("This may take several minutes...")
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit scan tasks
        future_to_ip = {
            executor.submit(run_nmap_scan, ip): ip 
            for ip in responding_hosts
        }
        
        # Collect results
        completed = 0
        for future in concurrent.futures.as_completed(future_to_ip):
            ip = future_to_ip[future]
            try:
                result = future.result()
                device_info[ip] = result
                completed += 1
                print(f"    Completed {completed}/{len(responding_hosts)}: {ip} -> {result['device_type']}")
            except Exception as e:
                print(f"    Error scanning {ip}: {e}")
                device_info[ip] = {
                    'ip': ip,
                    'device_type': 'Scan Error',
                    'os': '',
                    'services': '',
                    'vendor': '',
                    'hostname': ''
                }
    
    print(f"NMAP scanning completed!")
    return device_info


def get_device_summary(device_info: Dict[str, Dict[str, str]]) -> None:
    """Print a summary of discovered devices"""
    if not device_info:
        return
    
    print(f"\nDevice Type Summary:")
    print("-" * 30)
    
    type_counts = {}
    for info in device_info.values():
        device_type = info['device_type']
        type_counts[device_type] = type_counts.get(device_type, 0) + 1
    
    for device_type, count in sorted(type_counts.items()):
        print(f"  {device_type}: {count}")
    
    print(f"\nTotal devices scanned: {len(device_info)}")


def save_device_info(device_info: Dict[str, Dict[str, str]], filename: str) -> None:
    """Save device information to JSON file"""
    try:
        with open(filename, 'w') as f:
            json.dump(device_info, f, indent=2)
        print(f"Device information saved to: {filename}")
    except Exception as e:
        print(f"Error saving device info: {e}")


if __name__ == "__main__":
    # Test with a single IP
    test_ip = "***********"
    result = run_nmap_scan(test_ip)
    print(f"Test scan result for {test_ip}:")
    for key, value in result.items():
        print(f"  {key}: {value}")
