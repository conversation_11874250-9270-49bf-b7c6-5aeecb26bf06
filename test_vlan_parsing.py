#!/usr/bin/env python3
"""
Test VLAN interface parsing with the actual config format
"""

from switch_parser import extract_interface_config, extract_subnet_mask
import ipaddress


def test_vlan_parsing():
    """Test parsing of VLAN interface configuration"""
    
    # Sample running config with the actual format you provided
    sample_config = """
!
interface GigabitEthernet1/0/1
 switchport mode access
 switchport access vlan 100
!
interface Vlan500
 description * MGMT *
 ip address ************ *************
!
interface GigabitEthernet1/0/2
 switchport mode trunk
!
"""
    
    print("Testing VLAN interface parsing...")
    print("Sample config:")
    print(sample_config)
    print("-" * 50)
    
    # Test extracting Vlan500 config
    interface_config = extract_interface_config(sample_config, "Vlan500")
    print(f"Extracted interface config for Vlan500:")
    print(f"Length: {len(interface_config)} characters")
    print("Content:")
    print(repr(interface_config))
    print("\nFormatted content:")
    print(interface_config)
    print("-" * 50)
    
    # Test extracting subnet mask
    if interface_config:
        subnet_mask = extract_subnet_mask(interface_config)
        print(f"Extracted subnet mask: {subnet_mask}")
        
        if subnet_mask:
            # Test network calculation
            ip = "************"
            try:
                network = ipaddress.IPv4Network(f"{ip}/{subnet_mask}", strict=False)
                print(f"Calculated network: {network}")
                print(f"Network address: {network.network_address}")
                print(f"Broadcast address: {network.broadcast_address}")
                print(f"Number of hosts: {network.num_addresses}")
            except ValueError as e:
                print(f"Error calculating network: {e}")
        else:
            print("No subnet mask found!")
    else:
        print("No interface config found!")


if __name__ == "__main__":
    test_vlan_parsing()
