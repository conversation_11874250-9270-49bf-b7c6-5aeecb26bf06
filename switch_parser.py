#!/usr/bin/env python3
"""
Switch Parser Module
Handles parsing of switch list file and switch command outputs
"""

import re
import ipaddress
from typing import List, Dict, Tuple, Set
from dataclasses import dataclass


@dataclass
class SwitchInfo:
    """Data class to hold switch information"""
    name: str
    ip: str
    device_type: str
    username: str
    password: str
    enable_password: str


def parse_switch_list(filename: str) -> List[SwitchInfo]:
    """
    Parse the switch_list.txt file and return list of SwitchInfo objects
    
    Args:
        filename: Path to the switch list file
        
    Returns:
        List of SwitchInfo objects
    """
    switches = []
    
    try:
        with open(filename, 'r') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line:  # Skip empty lines
                    continue
                    
                parts = line.split()
                if len(parts) != 6:
                    print(f"Warning: Line {line_num} has {len(parts)} fields, expected 6. Skipping.")
                    continue
                
                switch = SwitchInfo(
                    name=parts[0],
                    ip=parts[1],
                    device_type=parts[2],
                    username=parts[3],
                    password=parts[4],
                    enable_password=parts[5]
                )
                switches.append(switch)
                
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        return []
    except Exception as e:
        print(f"Error reading {filename}: {e}")
        return []
    
    return switches


def parse_interface_ip(show_ip_int_brief: str) -> List[Tuple[str, str, str]]:
    """
    Parse 'show ip interface brief' output to extract interface IPs
    
    Args:
        show_ip_int_brief: Output from show ip interface brief command
        
    Returns:
        List of tuples (interface, ip, status)
    """
    interfaces = []
    lines = show_ip_int_brief.split('\n')
    
    for line in lines:
        # Match interface lines with IP addresses
        match = re.match(r'^(\S+)\s+(\d+\.\d+\.\d+\.\d+)\s+\S+\s+\S+\s+(\S+)', line.strip())
        if match:
            interface, ip, status = match.groups()
            if ip != "unassigned" and status == "up":
                interfaces.append((interface, ip, status))
    
    return interfaces


def extract_networks_from_interfaces(interfaces: List[Tuple[str, str, str]],
                                   show_run_output: str) -> Set[str]:
    """
    Extract network subnets from interface IPs using running config

    Args:
        interfaces: List of (interface, ip, status) tuples
        show_run_output: Output from show running-config

    Returns:
        Set of network subnets in CIDR notation
    """
    networks = set()

    for interface, ip, _ in interfaces:
        print(f"    Processing interface {interface} with IP {ip}")

        # Find subnet mask for this interface in running config
        interface_config = extract_interface_config(show_run_output, interface)
        subnet_mask = extract_subnet_mask(interface_config)

        if subnet_mask:
            try:
                # Create network object and get network address
                network = ipaddress.IPv4Network(f"{ip}/{subnet_mask}", strict=False)
                networks.add(str(network))
                print(f"    Added network: {network}")
            except ValueError as e:
                print(f"    Warning: Could not parse network for {interface} {ip}/{subnet_mask}: {e}")
        else:
            print(f"    Warning: No subnet mask found for {interface}")

    return networks


def extract_interface_config(show_run: str, interface: str) -> str:
    """Extract configuration for a specific interface from show run output"""
    lines = show_run.split('\n')
    interface_config = []
    in_interface = False

    # Handle different interface name formats (Vlan500 vs vlan500)
    interface_patterns = [
        f'interface {interface}',
        f'interface {interface.lower()}',
        f'interface {interface.capitalize()}'
    ]

    for line in lines:
        line_stripped = line.strip()

        # Check if this line starts any of our interface patterns
        interface_found = False
        for pattern in interface_patterns:
            if line_stripped == pattern or line_stripped.startswith(pattern + ' '):
                in_interface = True
                interface_config.append(line)
                interface_found = True
                break

        if interface_found:
            continue

        # If we're in an interface section
        if in_interface:
            # If we hit another interface line, we're done with this interface
            if line_stripped.startswith('interface '):
                break
            # If we hit a line that starts with '!' and is not indented, we might be done
            elif line_stripped == '!':
                interface_config.append(line)
                # Check if next non-empty line starts a new section
                continue
            else:
                interface_config.append(line)

    return '\n'.join(interface_config)


def extract_subnet_mask(interface_config: str) -> str:
    """Extract subnet mask from interface configuration"""
    lines = interface_config.split('\n')

    for line in lines:
        line_stripped = line.strip()
        # Look for ip address command with subnet mask
        match = re.search(r'ip address\s+\d+\.\d+\.\d+\.\d+\s+(\d+\.\d+\.\d+\.\d+)', line_stripped)
        if match:
            return match.group(1)

        # Also look for ip address command with CIDR notation (less common but possible)
        match = re.search(r'ip address\s+\d+\.\d+\.\d+\.\d+/(\d+)', line_stripped)
        if match:
            # Convert CIDR to subnet mask
            cidr = int(match.group(1))
            mask = (0xffffffff >> (32 - cidr)) << (32 - cidr)
            return f"{(mask >> 24) & 0xff}.{(mask >> 16) & 0xff}.{(mask >> 8) & 0xff}.{mask & 0xff}"

    return None


def normalize_interface_name(interface_name: str) -> str:
    """
    Normalize interface names to match MAC table format

    Args:
        interface_name: Full interface name (e.g., 'GigabitEthernet1/0/10')

    Returns:
        Abbreviated interface name (e.g., 'Gi1/0/10')
    """
    # Common interface name mappings
    mappings = {
        'GigabitEthernet': 'Gi',
        'FastEthernet': 'Fa',
        'TenGigabitEthernet': 'Te',
        'FortyGigabitEthernet': 'Fo',
        'HundredGigE': 'Hu',
        'Ethernet': 'Et',
        'Serial': 'Se',
        'Loopback': 'Lo',
        'Vlan': 'Vl'
    }

    for full_name, abbrev in mappings.items():
        if interface_name.startswith(full_name):
            return interface_name.replace(full_name, abbrev)

    return interface_name


def parse_cdp_neighbors(cdp_output: str) -> Dict[str, str]:
    """
    Parse CDP neighbor output to identify uplink ports
    
    Args:
        cdp_output: Output from show cdp neighbor detail
        
    Returns:
        Dictionary mapping local interface to remote device name
    """
    neighbors = {}
    lines = cdp_output.split('\n')
    current_device = None
    current_interface = None
    
    for line in lines:
        line = line.strip()
        
        # Look for device ID
        if line.startswith('Device ID:'):
            current_device = line.split(':', 1)[1].strip()
        
        # Look for local interface
        elif line.startswith('Interface:'):
            # Format: "Interface: GigabitEthernet0/1,  Port ID (outgoing port): GigabitEthernet0/2"
            match = re.search(r'Interface:\s*(\S+)', line)
            if match:
                interface_name = match.group(1)
                # Remove trailing comma and normalize interface name
                interface_name = interface_name.rstrip(',')
                # Convert full interface names to abbreviated format to match MAC table
                current_interface = normalize_interface_name(interface_name)
        
        # When we have both device and interface, store the mapping
        if current_device and current_interface:
            neighbors[current_interface] = current_device
            current_device = None
            current_interface = None
    
    return neighbors


def parse_mac_address_table(mac_table_output: str) -> List[Dict[str, str]]:
    """
    Parse MAC address table output
    
    Args:
        mac_table_output: Output from show mac address-table
        
    Returns:
        List of dictionaries with MAC table entries
    """
    mac_entries = []
    lines = mac_table_output.split('\n')
    
    for line in lines:
        # Match MAC address table entries
        # Format varies but typically: VLAN MAC Type Ports
        match = re.match(r'\s*(\d+)\s+([0-9a-fA-F]{4}\.[0-9a-fA-F]{4}\.[0-9a-fA-F]{4})\s+(\S+)\s+(.+)', line.strip())
        if match:
            vlan, mac, mac_type, ports = match.groups()
            mac_entries.append({
                'vlan': vlan,
                'mac': mac,
                'type': mac_type,
                'ports': ports.strip()
            })
    
    return mac_entries


def parse_interface_descriptions(show_run: str) -> Dict[str, str]:
    """
    Parse interface descriptions from running configuration

    Args:
        show_run: Output from show running-config

    Returns:
        Dictionary mapping interface names to descriptions
    """
    descriptions = {}
    lines = show_run.split('\n')
    current_interface = None

    for line in lines:
        line_stripped = line.strip()

        # Check for interface line
        if line_stripped.startswith('interface '):
            interface_name = line_stripped.replace('interface ', '')
            # Normalize interface name to match MAC table format
            current_interface = normalize_interface_name(interface_name)

        # Check for description line
        elif current_interface and line_stripped.startswith('description '):
            description = line_stripped.replace('description ', '')
            descriptions[current_interface] = description

        # Reset when we hit another interface or end of interface config
        elif line_stripped.startswith('interface ') or line_stripped == '!':
            if line_stripped.startswith('interface '):
                interface_name = line_stripped.replace('interface ', '')
                current_interface = normalize_interface_name(interface_name)
            # Don't reset current_interface on '!' as description might come after

    return descriptions


def parse_port_configurations(show_run: str) -> Dict[str, Dict[str, str]]:
    """
    Parse port configurations from running configuration

    Args:
        show_run: Output from show running-config

    Returns:
        Dictionary mapping interface names to port configuration
    """
    port_configs = {}
    lines = show_run.split('\n')
    current_interface = None
    current_config = {}

    for line in lines:
        line_stripped = line.strip()

        # Check for interface line
        if line_stripped.startswith('interface '):
            # Save previous interface config if exists
            if current_interface and current_config:
                port_configs[current_interface] = current_config.copy()

            # Start new interface
            interface_name = line_stripped.replace('interface ', '')
            current_interface = normalize_interface_name(interface_name)
            current_config = {
                'mode': 'access',  # Default
                'access_vlan': '1',  # Default
                'allowed_vlans': '',
                'native_vlan': ''
            }

        elif current_interface:
            # Parse switchport mode
            if line_stripped.startswith('switchport mode '):
                mode = line_stripped.replace('switchport mode ', '')
                current_config['mode'] = mode

            # Parse access VLAN
            elif line_stripped.startswith('switchport access vlan '):
                vlan = line_stripped.replace('switchport access vlan ', '')
                current_config['access_vlan'] = vlan

            # Parse trunk allowed VLANs
            elif line_stripped.startswith('switchport trunk allowed vlan '):
                vlans = line_stripped.replace('switchport trunk allowed vlan ', '')
                current_config['allowed_vlans'] = vlans

            # Parse trunk native VLAN
            elif line_stripped.startswith('switchport trunk native vlan '):
                vlan = line_stripped.replace('switchport trunk native vlan ', '')
                current_config['native_vlan'] = vlan

            # End of interface config
            elif line_stripped.startswith('interface ') or line_stripped == '!':
                if line_stripped.startswith('interface '):
                    # Save current config and start new interface
                    if current_interface and current_config:
                        port_configs[current_interface] = current_config.copy()

                    interface_name = line_stripped.replace('interface ', '')
                    current_interface = normalize_interface_name(interface_name)
                    current_config = {
                        'mode': 'access',
                        'access_vlan': '1',
                        'allowed_vlans': '',
                        'native_vlan': ''
                    }

    # Save last interface config
    if current_interface and current_config:
        port_configs[current_interface] = current_config.copy()

    return port_configs


def parse_arp_table(arp_output: str) -> List[Dict[str, str]]:
    """
    Parse ARP table output
    
    Args:
        arp_output: Output from show ip arp
        
    Returns:
        List of dictionaries with ARP entries
    """
    arp_entries = []
    lines = arp_output.split('\n')
    
    for line in lines:
        # Match ARP entries
        # Format: Protocol Address Age(min) Hardware Addr Type Interface
        match = re.match(r'\s*(\S+)\s+(\d+\.\d+\.\d+\.\d+)\s+(\S+)\s+([0-9a-fA-F]{4}\.[0-9a-fA-F]{4}\.[0-9a-fA-F]{4})\s+(\S+)\s+(.+)', line.strip())
        if match:
            protocol, ip, age, mac, arp_type, interface = match.groups()
            arp_entries.append({
                'protocol': protocol,
                'ip': ip,
                'age': age,
                'mac': mac,
                'type': arp_type,
                'interface': interface.strip()
            })
    
    return arp_entries
