#!/usr/bin/env python3
"""
<PERSON>ript to verify the Excel file structure and content
"""

import pandas as pd
import openpyxl


def verify_excel_file(filename):
    """Verify the Excel file structure and content"""
    print(f"Verifying Excel file: {filename}")
    print("=" * 50)
    
    try:
        # Load the workbook to check sheet names
        workbook = openpyxl.load_workbook(filename)
        print(f"Sheet names: {workbook.sheetnames}")
        
        # Load with pandas to check content
        excel_file = pd.ExcelFile(filename)
        print(f"Pandas sheet names: {excel_file.sheet_names}")
        
        # Check the Network mapping sheet
        if 'Network mapping' in excel_file.sheet_names:
            df = pd.read_excel(filename, sheet_name='Network mapping')
            print(f"\nNetwork mapping sheet:")
            print(f"  Columns: {list(df.columns)}")
            print(f"  Rows: {len(df)}")
            print(f"  Sample data:")
            print(df.to_string())
            
            # Check column widths
            worksheet = workbook['Network mapping']
            print(f"\nColumn widths:")
            for col_letter in ['A', 'B', 'C', 'D', 'E', 'F', 'G']:
                if col_letter in [chr(65 + i) for i in range(len(df.columns))]:
                    width = worksheet.column_dimensions[col_letter].width
                    print(f"  Column {col_letter}: {width}")
        else:
            print("Network mapping sheet not found!")
            
        workbook.close()
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")


if __name__ == "__main__":
    # Find the latest Excel file
    import glob
    excel_files = glob.glob("network_inventory_*.xlsx")
    if excel_files:
        latest_file = max(excel_files)
        verify_excel_file(latest_file)
    else:
        print("No Excel files found!")
