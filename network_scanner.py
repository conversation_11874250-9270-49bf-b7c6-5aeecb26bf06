#!/usr/bin/env python3
"""
Network Scanner Module
Handles network scanning and ping operations
"""

import subprocess
import ipaddress
import concurrent.futures
import time
from typing import List, Set, Dict
import platform


def ping_host(ip: str, timeout: int = 1) -> bool:
    """
    Ping a single host
    
    Args:
        ip: IP address to ping
        timeout: Timeout in seconds
        
    Returns:
        True if host responds, False otherwise
    """
    try:
        # Determine ping command based on OS (fast ping)
        if platform.system().lower() == "windows":
            cmd = ["ping", "-n", "1", "-w", "500", ip]  # 500ms timeout
        else:
            cmd = ["ping", "-c", "1", "-W", "1", ip]  # 1 second timeout
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 2)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, subprocess.SubprocessError):
        return False


def ping_sweep_network(network: str, max_workers: int = 100) -> List[str]:
    """
    Perform ping sweep on a network
    
    Args:
        network: Network in CIDR notation (e.g., '***********/24')
        max_workers: Maximum number of concurrent ping operations
        
    Returns:
        List of responding IP addresses
    """
    try:
        net = ipaddress.IPv4Network(network, strict=False)
        responding_hosts = []
        
        print(f"Scanning network {network} ({net.num_addresses} addresses)...")
        
        # Skip network and broadcast addresses for /24 and smaller
        if net.prefixlen >= 24:
            hosts = list(net.hosts())
        else:
            # For larger networks, sample or limit the scan
            hosts = list(net.hosts())
            if len(hosts) > 1000:
                print(f"Warning: Large network {network} has {len(hosts)} hosts. Limiting scan to first 1000.")
                hosts = hosts[:1000]
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit ping tasks
            future_to_ip = {executor.submit(ping_host, str(ip)): str(ip) for ip in hosts}
            
            # Collect results
            for future in concurrent.futures.as_completed(future_to_ip):
                ip = future_to_ip[future]
                try:
                    if future.result():
                        responding_hosts.append(ip)
                        print(f"  {ip} - UP")
                except Exception as e:
                    print(f"  {ip} - Error: {e}")
        
        print(f"Network {network}: {len(responding_hosts)} hosts responding")
        return responding_hosts
        
    except ValueError as e:
        print(f"Error: Invalid network {network}: {e}")
        return []


def ping_sweep_all_networks(networks: Set[str], max_workers: int = 100) -> Dict[str, List[str]]:
    """
    Perform ping sweep on multiple networks
    
    Args:
        networks: Set of networks in CIDR notation
        max_workers: Maximum number of concurrent operations
        
    Returns:
        Dictionary mapping network to list of responding hosts
    """
    results = {}
    
    print(f"\nStarting ping sweep of {len(networks)} networks...")
    print("This will help populate ARP tables on switches.\n")
    
    for network in sorted(networks):
        print(f"\n{'='*60}")
        responding_hosts = ping_sweep_network(network, max_workers)
        results[network] = responding_hosts
        
        # Small delay between networks to avoid overwhelming the network
        time.sleep(1)
    
    print(f"\n{'='*60}")
    print("Ping sweep completed!")
    
    # Summary
    total_responding = sum(len(hosts) for hosts in results.values())
    print(f"Total networks scanned: {len(networks)}")
    print(f"Total responding hosts: {total_responding}")
    
    return results


def validate_network_reachability(networks: Set[str]) -> Set[str]:
    """
    Validate that networks are reachable before full scan
    
    Args:
        networks: Set of networks to validate
        
    Returns:
        Set of reachable networks
    """
    reachable_networks = set()
    
    print("Validating network reachability...")
    
    for network in networks:
        try:
            net = ipaddress.IPv4Network(network, strict=False)
            # Try to ping the first few hosts in each network
            test_hosts = list(net.hosts())[:3]
            
            reachable = False
            for host in test_hosts:
                if ping_host(str(host), timeout=2):
                    reachable = True
                    break
            
            if reachable:
                reachable_networks.add(network)
                print(f"  {network} - Reachable")
            else:
                print(f"  {network} - Not reachable (skipping)")
                
        except ValueError as e:
            print(f"  {network} - Invalid network: {e}")
    
    return reachable_networks


def get_local_networks() -> Set[str]:
    """
    Get local networks from the system's routing table
    This helps identify which networks are directly reachable
    
    Returns:
        Set of local networks
    """
    local_networks = set()
    
    try:
        if platform.system().lower() == "windows":
            result = subprocess.run(["route", "print"], capture_output=True, text=True)
        else:
            result = subprocess.run(["ip", "route"], capture_output=True, text=True)
        
        if result.returncode == 0:
            # Parse routing table to extract local networks
            # This is a simplified implementation
            lines = result.stdout.split('\n')
            for line in lines:
                # Look for network routes (this is OS-specific and simplified)
                if '/' in line and ('eth' in line or 'en' in line or 'wlan' in line):
                    parts = line.split()
                    for part in parts:
                        if '/' in part and not part.startswith('169.254'):
                            try:
                                net = ipaddress.IPv4Network(part, strict=False)
                                local_networks.add(str(net))
                            except ValueError:
                                continue
    
    except Exception as e:
        print(f"Warning: Could not determine local networks: {e}")
    
    return local_networks
