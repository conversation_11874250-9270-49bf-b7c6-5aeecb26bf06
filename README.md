# Network Discovery Tool

A comprehensive Python-based network discovery tool for reverse-engineering network infrastructure. This tool safely connects to Cisco switches to gather network topology, device inventory, and connectivity information without making any configuration changes.

## Features

### Phase 1: Network Discovery
- Connects to all switches in your inventory
- Discovers all Layer 3 interfaces and networks
- Performs network ping sweeps to populate ARP tables
- Identifies unique network subnets

### Phase 2: Device Analysis
- Gathers MAC address tables from all switches
- Collects ARP tables for IP-to-MAC correlation
- Retrieves interface status and descriptions
- Identifies uplink ports using CDP neighbor discovery
- Maps devices to switch ports with VLAN information

### Phase 3: Reporting
- Generates executive summary reports
- Creates detailed switch analysis
- Produces comprehensive port analysis
- Exports data to Excel format for further analysis
- Correlates MAC addresses with IP addresses

## Safety Features

- **READ-ONLY OPERATIONS**: Never uses `conf t` or any configuration commands
- **NO CONFIGURATION CHANGES**: Only executes show commands
- **PRODUCTION SAFE**: Designed for use on live production networks
- **ERROR HANDLING**: Graceful handling of connection failures and timeouts

## Requirements

- Python 3.7+
- Network access to target switches
- Valid credentials for switch access

## Installation

1. Install required packages:
```bash
pip install -r requirements.txt
```

2. Prepare your switch list file (`switch_list.txt`) with the format:
```
switch_name ip_address device_type username password enable_password
```

Example:
```
sw183-ISP1 *********** cisco_ios admin sw183 swit83
sw183-ISP2 ************ cisco_ios admin sw183 swit83
```

## Usage

### Basic Usage
```bash
python network_discovery.py
```

### Custom Switch List File
```bash
python network_discovery.py my_switches.txt
```

## Output Files

The tool generates several output files:

1. **network_discovery_data.json** - Raw data in JSON format
2. **executive_summary_TIMESTAMP.txt** - High-level summary
3. **switch_details_TIMESTAMP.txt** - Detailed switch information
4. **port_analysis_TIMESTAMP.txt** - Port-by-port analysis
5. **network_inventory_TIMESTAMP.xlsx** - Excel spreadsheet with multiple sheets

## Report Contents

### Executive Summary
- Switch connectivity status
- Network discovery results
- Scan statistics
- High-level switch information

### Switch Details
- Layer 3 interface inventory
- Network subnets per switch
- CDP neighbor relationships (uplinks)
- MAC and ARP table summaries

### Port Analysis
- Port-by-port device mapping
- MAC address to IP address correlation
- VLAN assignments
- Uplink identification
- Device descriptions where available

### Excel Inventory
- **Switch Inventory**: Complete switch information
- **Network Inventory**: All discovered networks
- **Device Inventory**: All discovered devices with correlation

## Supported Commands

The tool executes these read-only commands on each switch:

- `show ip interface brief` - Layer 3 interfaces
- `show cdp neighbors detail` - Uplink identification
- `show mac address-table` - MAC address learning
- `show ip arp` - IP to MAC correlation
- `show interfaces status` - Port status and descriptions
- `show running-config` - Interface configurations (for subnet masks)

## Architecture

The tool is modular with separate components:

- **network_discovery.py** - Main orchestration script
- **switch_parser.py** - Command output parsing utilities
- **network_scanner.py** - Network scanning and ping operations
- **report_generator.py** - Report generation in multiple formats

## Error Handling

- Connection timeouts are handled gracefully
- Authentication failures are logged and reported
- Individual switch failures don't stop the entire discovery
- Partial data collection continues even if some commands fail

## Security Considerations

- Credentials are only stored in memory during execution
- No configuration changes are ever attempted
- All operations are read-only
- Supports enable passwords for privileged access

## Troubleshooting

### Common Issues

1. **Connection Timeouts**
   - Verify network connectivity to switches
   - Check firewall rules for SSH/Telnet access
   - Increase timeout values if needed

2. **Authentication Failures**
   - Verify credentials in switch_list.txt
   - Ensure enable passwords are correct
   - Check if accounts are locked

3. **Missing Data**
   - Some switches may not support all commands
   - CDP may be disabled on some interfaces
   - Check switch logs for any issues

### Debug Mode

For troubleshooting, you can modify the script to enable debug logging by adding:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

This tool is designed to be extensible. You can:

- Add support for additional device types
- Extend parsing for different command outputs
- Add new report formats
- Enhance network scanning capabilities

## License

This tool is provided as-is for network documentation and analysis purposes.
