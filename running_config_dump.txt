Building configuration...

Current configuration : 11850 bytes
!
! Last configuration change at 07:11:50 EDT Fri Nov 17 2023
!
version 15.2
no service pad
service timestamps debug datetime msec
service timestamps log datetime msec
service password-encryption
!
hostname sw183-ISP2
!
boot-start-marker
boot-end-marker
!
enable secret 9 $9$rh5XVfy4GSiQCH$/vM9U5WOLW1SS8zIAIm9WP.yWFCkPEXuLO7YcDq8E3I
enable password 7 0100110D4F5355
!
username admin privilege 15 password 7 0215130A5355
aaa new-model
!
!
!
!
!
!
!
!
aaa session-id common
clock timezone EDT -5 0
clock summer-time EDT recurring
switch 1 provision c1000-8fp-2g-l
system mtu routing 1500
!
!
ip dhcp snooping vlan 100-302
ip domain-lookup source-interface Vlan500
ip domain-name canamgroupinc.com
ip name-server ***********
ip name-server *************
login on-success log
vtp mode transparent
!
!
!
!
!
mls qos srr-queue output cos-map queue 1 threshold 1 4
mls qos srr-queue output cos-map queue 2 threshold 1 2 6 7
mls qos srr-queue output cos-map queue 2 threshold 2 3
mls qos srr-queue output cos-map queue 3 threshold 2 0
mls qos srr-queue output cos-map queue 4 threshold 2 1
mls qos srr-queue output dscp-map queue 1 threshold 2 32 33 40 41 42 43 44 45
mls qos srr-queue output dscp-map queue 1 threshold 2 46 47
mls qos srr-queue output dscp-map queue 2 threshold 1 16 17 18 19 20 21 22 23
mls qos srr-queue output dscp-map queue 2 threshold 1 26 27 28 29 30 31 34 35
mls qos srr-queue output dscp-map queue 2 threshold 1 36 37 38 39
mls qos srr-queue output dscp-map queue 2 threshold 2 24 48 49 50 51 52 53 54
mls qos srr-queue output dscp-map queue 2 threshold 2 55 56 57 58 59 60 61 62
mls qos srr-queue output dscp-map queue 2 threshold 2 63
mls qos srr-queue output dscp-map queue 3 threshold 1 0 1 2 3 4 5 6 7
mls qos srr-queue output dscp-map queue 4 threshold 1 8 9 10 11 12 13 14 15
mls qos
!
crypto pki trustpoint SLA-TrustPoint
 revocation-check crl
!
crypto pki trustpoint TP-self-signed-488774400
 enrollment selfsigned
 subject-name cn=IOS-Self-Signed-Certificate-488774400
 revocation-check none
 rsakeypair TP-self-signed-488774400
!
!
crypto pki certificate chain SLA-TrustPoint
crypto pki certificate chain TP-self-signed-488774400
 certificate self-signed 01
  30820229 30820192 A0030201 02020101 300D0609 2A864886 F70D0101 05050030 
  30312E30 2C060355 04031325 494F532D 53656C66 2D536967 6E65642D 43657274 
  69666963 6174652D 34383837 37343430 30301E17 0D323330 33333131 33333031 
  375A170D 33303031 30313030 30303030 5A303031 2E302C06 03550403 1325494F 
  532D5365 6C662D53 69676E65 642D4365 72746966 69636174 652D3438 38373734 
  34303030 819F300D 06092A86 4886F70D 01010105 0003818D 00308189 02818100 
  AF010F4D 7075E558 B9C57EEC 1F57A5B8 1E1415D2 7CC2EF24 64E48ABF D1A62BD9 
  83940F1A FB247F81 B48145F6 B415E2A4 9CD2719B 034FA3F3 0E77FA21 F0A773E0 
  8F5C473F 5837E307 970C7416 12358EA5 B6A5DB1C 27004CCF 1EDA130D EBC698A4 
  00423C2A 39182816 84728A49 FF547F9E E9043CFD B9CA4163 CA1EE403 51427B1B 
  02030100 01A35330 51300F06 03551D13 0101FF04 05300301 01FF301F 0603551D 
  23041830 168014D9 BBC3BD76 9E7D3D33 E6FD302B 9FA46F65 6E029D30 1D060355 
  1D0E0416 0414D9BB C3BD769E 7D3D33E6 FD302B9F A46F656E 029D300D 06092A86 
  4886F70D 01010505 00038181 002B1870 0941D934 E5546B86 F6C6DC38 F074CAF6 
  23D70FD7 A9930B81 813937D6 874D5B02 7E5DA591 CF8B707D BB342EA0 2CD47D77 
  BFC9B0C2 C10858D6 50FE4A6C 75AE2DB8 52E308EF 1C1D956B 1B4A7B44 E00E7E71 
  61564367 ACEA9C7F 79F0C0ED 10AE7419 2E030610 0D1FAD23 4E8B990F 69453D22 
  B63BF733 7077DD44 72CC7E27 08
  	quit
memory free low-watermark processor 23433
!
spanning-tree mode rapid-pvst
spanning-tree loopguard default
spanning-tree portfast edge default
spanning-tree portfast edge bpduguard default
spanning-tree extend system-id
spanning-tree uplinkfast
spanning-tree backbonefast
spanning-tree vlan 500 priority 36864
auto qos srnd4
errdisable recovery cause udld
errdisable recovery cause bpduguard
errdisable recovery cause security-violation
errdisable recovery cause pagp-flap
errdisable recovery cause dtp-flap
errdisable recovery cause link-flap
errdisable recovery cause sfp-config-mismatch
errdisable recovery cause gbic-invalid
errdisable recovery cause psecure-violation
errdisable recovery cause dhcp-rate-limit
errdisable recovery cause storm-control
errdisable recovery cause arp-inspection
errdisable recovery cause loopback
errdisable recovery interval 60
!
vlan internal allocation policy ascending
!
vlan 451
 name ISP2
!
vlan 500
 name MGMT
!
!
class-map match-any system-cpp-police-ewlc-control
  description EWLC Control 
class-map match-any system-cpp-police-topology-control
  description Topology control
class-map match-any system-cpp-police-sw-forward
  description Sw forwarding, L2 LVX data packets, LOGGING, Transit Traffic
class-map match-any system-cpp-default
  description EWLC data, Inter FED Traffic 
class-map match-any system-cpp-police-sys-data
  description Openflow, Exception, EGR Exception, NFL Sampled Data, RPF Failed
class-map match-any system-cpp-police-punt-webauth
  description Punt Webauth
class-map match-any system-cpp-police-l2lvx-control
  description L2 LVX control packets
class-map match-any system-cpp-police-forus
  description Forus Address resolution and Forus traffic
class-map match-any system-cpp-police-multicast-end-station
  description MCAST END STATION
class-map match-any system-cpp-police-high-rate-app
  description High Rate Applications 
class-map match-any system-cpp-police-multicast
  description MCAST Data
class-map match-any system-cpp-police-l2-control
  description L2 control
class-map match-any system-cpp-police-dot1x-auth
  description DOT1X Auth
class-map match-any system-cpp-police-data
  description ICMP redirect, ICMP_GEN and BROADCAST
class-map match-any system-cpp-police-stackwise-virt-control
  description Stackwise Virtual OOB
class-map match-any non-client-nrt-class
class-map match-any system-cpp-police-routing-control
  description Routing control and Low Latency
class-map match-any system-cpp-police-protocol-snooping
  description Protocol snooping
class-map match-any system-cpp-police-dhcp-snooping
  description DHCP snooping
class-map match-any system-cpp-police-ios-routing
  description L2 control, Topology control, Routing control, Low Latency
class-map match-any system-cpp-police-system-critical
  description System Critical and Gold Pkt
class-map match-any system-cpp-police-ios-feature
  description ICMPGEN,BROADCAST,ICMP,L2LVXCntrl,ProtoSnoop,PuntWebauth,MCASTData,Transit,DOT1XAuth,Swfwd,LOGGING,L2LVXData,ForusTraffic,ForusARP,McastEndStn,Openflow,Exception,EGRExcption,NflSampled,RpfFailed
!
!
!
macro name APS
 switchport access vlan 300
 switchport mode access
 switchport nonegotiate
 srr-queue bandwidth share 10 10 60 20
 srr-queue bandwidth shape  10  0  0  0
 queue-set 2
 mls qos trust device cisco-phone
 mls qos trust dscp
 auto qos voip cisco-phone
 storm-control broadcast level 1.00
 spanning-tree portfast
 spanning-tree guard root
 ip dhcp snooping limit rate 5
@
macro name TRUNK
 description * Trunk to <sw_name> *
 switchport trunk encapsulation dot1q
 switchport trunk allowed vlan all
 switchport mode trunk
 switchport nonegotiate
 srr-queue bandwidth share 10 10 60 20
 srr-queue bandwidth shape  10  0  0  0
 queue-set 2
 auto qos voip trust
 mls qos trust dscp
 spanning-tree portfast disable
 spanning-tree bpdufilter disable
 spanning-tree bpduguard disable
 spanning-tree guard none
 ip dhcp snooping trust
 no shutdown
 mls qos trust dscp
@
macro name NO
 no description
 no switchport access vlan 100
 no switchport trunk encapsulation dot1q
 switchport mode access
 no switchport trunk allowed vlan 100,300-302
 no switchport voice vlan 301
 no switchport nonegotiate
 no switchport port-security
 no switchport port-security maximum 3
 no switchport port-security aging time 2
 no switchport port-security violation restrict
 no switchport port-security aging type inactivity
 no srr-queue bandwidth share 10 10 60 20
 no srr-queue bandwidth shape  10  0  0  0
 no queue-set 2
 no mls qos trust dscp
 no mls qos trust device cisco-phone
 no macro description
 no auto qos voip trust
 no storm-control broadcast level 1.00
 no spanning-tree portfast disable
 no spanning-tree bpdufilter disable
 no spanning-tree bpduguard disable
 no spanning-tree guard
 no ip dhcp snooping limit rate 5
 no ip dhcp snooping trust
@
macro name VOICE
 description * Voice et Data *
 switchport mode access
 switchport nonegotiate
 switchport access vlan 100
 switchport voice vlan 301
 switchport port-security
 switchport port-security maximum 3
 switchport port-security violation restrict
 switchport port-security aging time 2
 switchport port-security aging type inactivity
 spanning-tree guard root
 switchport priority extend cos 0
 auto qos voip cisco-phone
 mls qos trust dscp
 mls qos trust device cisco-phone
 storm-control broadcast level 1.00
 ip dhcp snooping limit rate 5
 no shutdown
@
macro name DATA
 description * Data *
 switchport mode access
 switchport nonegotiate
 switchport access vlan  100
 switchport port-security
 switchport port-security maximum 1
 switchport port-security violation restrict
 switchport port-security aging time 2
 switchport port-security aging type inactivity
 spanning-tree guard root
 mls qos trust dscp
 storm-control broadcast level 1.00
 ip dhcp snooping limit rate 5
 no shutdown
@
!
!
interface GigabitEthernet1/0/1
 description * ISP2 *
 switchport access vlan 451
 switchport mode access
 switchport nonegotiate
 switchport port-security violation restrict
 switchport port-security aging time 2
 switchport port-security aging type inactivity
 switchport port-security
 mls qos trust dscp
 macro description DATA
 storm-control broadcast level 1.00
 spanning-tree guard root
 ip dhcp snooping limit rate 5
!
interface GigabitEthernet1/0/2
 description * ISP2 *
 switchport access vlan 451
 switchport mode access
 switchport nonegotiate
 switchport port-security violation restrict
 switchport port-security aging time 2
 switchport port-security aging type inactivity
 switchport port-security
 mls qos trust dscp
 macro description DATA
 storm-control broadcast level 1.00
 spanning-tree guard root
 ip dhcp snooping limit rate 5
!
interface GigabitEthernet1/0/3
 description * ISP2 *
 switchport access vlan 451
 switchport mode access
 switchport nonegotiate
 switchport port-security violation restrict
 switchport port-security aging time 2
 switchport port-security aging type inactivity
 switchport port-security
 mls qos trust dscp
 macro description DATA
 storm-control broadcast level 1.00
 spanning-tree guard root
 ip dhcp snooping limit rate 5
!
interface GigabitEthernet1/0/4
 shutdown
!
interface GigabitEthernet1/0/5
 shutdown
!
interface GigabitEthernet1/0/6
 shutdown
!
interface GigabitEthernet1/0/7
 shutdown
!
interface GigabitEthernet1/0/8
 shutdown
!
interface GigabitEthernet1/0/9
 shutdown
!
interface GigabitEthernet1/0/10
 description * Trk to sw143-0 *
 switchport trunk allowed vlan 500
 switchport mode trunk
 switchport nonegotiate
 mls qos trust cos
 srr-queue bandwidth share 1 30 35 5
 srr-queue bandwidth shape  10 0 0 0
 priority-queue out 
 macro description TRUNK
 auto qos trust 
 spanning-tree portfast disable
 spanning-tree bpdufilter disable
 spanning-tree bpduguard disable
 spanning-tree guard none
 ip dhcp snooping trust
!
interface Vlan1
 no ip address
 shutdown
!
interface Vlan500
 description * MGMT *
 ip address ***********0 *************
!
ip default-gateway ***********
ip http server
ip http authentication local
ip http secure-server
!
access-list 1 permit 10.0.0.0 *************
!
!
!
!
!
line con 0
 stopbits 1
line vty 0 4
 access-class 1 in
 exec-timeout 30 0
 length 25
 transport input telnet ssh
line vty 5 15
 access-class 1 in
 exec-timeout 30 0
 length 25
 transport input telnet ssh
!
end
