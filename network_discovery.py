#!/usr/bin/env python3
"""
Network Discovery Script
Main script for comprehensive network discovery and switch analysis
"""

import sys
import time
import json
from datetime import datetime
from typing import Dict, List, Set
from netmiko import ConnectHandler
from netmiko.exceptions import NetmikoTimeoutException, NetmikoAuthenticationException
import concurrent.futures

from switch_parser import (
    parse_switch_list, SwitchInfo, parse_interface_ip,
    extract_networks_from_interfaces, parse_cdp_neighbors,
    parse_mac_address_table, parse_arp_table, parse_interface_descriptions,
    parse_port_configurations
)
from network_scanner import ping_sweep_all_networks, validate_network_reachability
from device_identifier import scan_responding_hosts, get_device_summary, save_device_info
from report_generator import generate_comprehensive_report


class NetworkDiscovery:
    """Main class for network discovery operations"""
    
    def __init__(self, switch_list_file: str = "switch_list.txt"):
        self.switch_list_file = switch_list_file
        self.switches = []
        self.discovered_networks = set()
        self.switch_data = {}
        self.ping_results = {}
        self.device_info = {}
        
    def load_switches(self) -> bool:
        """Load switch information from file"""
        print("Loading switch information...")
        self.switches = parse_switch_list(self.switch_list_file)
        
        if not self.switches:
            print("Error: No switches loaded from file")
            return False
            
        print(f"Loaded {len(self.switches)} switches:")
        for switch in self.switches:
            print(f"  - {switch.name} ({switch.ip})")
        
        return True
    
    def connect_to_switch(self, switch: SwitchInfo) -> Dict:
        """
        Connect to a single switch and gather information
        
        Args:
            switch: SwitchInfo object
            
        Returns:
            Dictionary with switch data or error information
        """
        print(f"\nConnecting to {switch.name} ({switch.ip})...")
        
        device = {
            'device_type': switch.device_type,
            'host': switch.ip,
            'username': switch.username,
            'password': switch.password,
            'secret': switch.enable_password,
            'timeout': 30,
            'session_timeout': 60,
        }
        
        switch_data = {
            'name': switch.name,
            'ip': switch.ip,
            'connected': False,
            'error': None,
            'interfaces': [],
            'networks': set(),
            'cdp_neighbors': {},
            'mac_table': [],
            'arp_table': [],
            'interface_descriptions': {},
            'port_configurations': {},
            'interface_status': '',
            'running_config': ''
        }
        
        try:
            with ConnectHandler(**device) as connection:
                print(f"  Connected to {switch.name}")

                # Ensure we're in enable mode for full access
                prompt = connection.find_prompt()
                if not prompt.endswith('#'):
                    print(f"    Entering enable mode...")
                    connection.enable()

                # Gather all required information
                commands = {
                    'show_ip_int_brief': 'show ip interface brief',
                    'show_cdp_neighbors': 'show cdp neighbors detail',
                    'show_mac_table': 'show mac address-table',
                    'show_arp': 'show ip arp',
                    'show_int_status': 'show interfaces status',
                    'show_run': 'show running-config'
                }
                
                outputs = {}
                for cmd_name, command in commands.items():
                    try:
                        print(f"    Executing: {command}")
                        outputs[cmd_name] = connection.send_command(command, delay_factor=2)
                        time.sleep(0.5)  # Small delay between commands
                    except Exception as e:
                        print(f"    Warning: Failed to execute {command}: {e}")
                        outputs[cmd_name] = ""
                
                # Parse the outputs
                switch_data['connected'] = True
                
                # Parse interfaces and extract networks
                if outputs.get('show_ip_int_brief'):
                    switch_data['interfaces'] = parse_interface_ip(outputs['show_ip_int_brief'])
                    if outputs.get('show_run'):
                        switch_data['networks'] = extract_networks_from_interfaces(
                            switch_data['interfaces'], outputs['show_run']
                        )
                
                # Parse CDP neighbors
                if outputs.get('show_cdp_neighbors'):
                    switch_data['cdp_neighbors'] = parse_cdp_neighbors(outputs['show_cdp_neighbors'])
                
                # Parse MAC address table
                if outputs.get('show_mac_table'):
                    switch_data['mac_table'] = parse_mac_address_table(outputs['show_mac_table'])
                
                # Parse ARP table
                if outputs.get('show_arp'):
                    switch_data['arp_table'] = parse_arp_table(outputs['show_arp'])

                # Parse interface descriptions
                if outputs.get('show_run'):
                    switch_data['interface_descriptions'] = parse_interface_descriptions(outputs['show_run'])
                    switch_data['port_configurations'] = parse_port_configurations(outputs['show_run'])

                # Store raw outputs for report generation
                switch_data['interface_status'] = outputs.get('show_int_status', '')
                switch_data['running_config'] = outputs.get('show_run', '')
                
                print(f"  Successfully gathered data from {switch.name}")
                print(f"    - {len(switch_data['interfaces'])} L3 interfaces")
                print(f"    - {len(switch_data['networks'])} networks")
                print(f"    - {len(switch_data['cdp_neighbors'])} CDP neighbors")
                print(f"    - {len(switch_data['mac_table'])} MAC entries")
                print(f"    - {len(switch_data['arp_table'])} ARP entries")
                
        except NetmikoAuthenticationException as e:
            error_msg = f"Authentication failed for {switch.name}: {e}"
            print(f"  Error: {error_msg}")
            switch_data['error'] = error_msg
            
        except NetmikoTimeoutException as e:
            error_msg = f"Connection timeout for {switch.name}: {e}"
            print(f"  Error: {error_msg}")
            switch_data['error'] = error_msg
            
        except Exception as e:
            error_msg = f"Unexpected error connecting to {switch.name}: {e}"
            print(f"  Error: {error_msg}")
            switch_data['error'] = error_msg
        
        return switch_data
    
    def discover_networks(self, max_workers: int = 5) -> bool:
        """
        Connect to all switches and discover networks
        
        Args:
            max_workers: Maximum number of concurrent switch connections
            
        Returns:
            True if successful, False otherwise
        """
        print(f"\n{'='*60}")
        print("PHASE 1: NETWORK DISCOVERY")
        print(f"{'='*60}")
        
        # Connect to switches concurrently (but limited to avoid overwhelming)
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_switch = {
                executor.submit(self.connect_to_switch, switch): switch 
                for switch in self.switches
            }
            
            for future in concurrent.futures.as_completed(future_to_switch):
                switch = future_to_switch[future]
                try:
                    switch_data = future.result()
                    self.switch_data[switch.name] = switch_data
                    
                    # Add networks to global set
                    if switch_data['connected']:
                        self.discovered_networks.update(switch_data['networks'])
                        
                except Exception as e:
                    print(f"Error processing {switch.name}: {e}")
        
        # Summary
        connected_switches = sum(1 for data in self.switch_data.values() if data['connected'])
        print(f"\nDiscovery Summary:")
        print(f"  - Connected to {connected_switches}/{len(self.switches)} switches")
        print(f"  - Discovered {len(self.discovered_networks)} unique networks")
        
        if self.discovered_networks:
            print("  - Networks found:")
            for network in sorted(self.discovered_networks):
                print(f"    * {network}")
        
        return len(self.discovered_networks) > 0
    
    def perform_network_scan(self) -> bool:
        """Perform ping sweep of discovered networks"""
        if not self.discovered_networks:
            print("No networks to scan")
            return False
        
        print(f"\n{'='*60}")
        print("PHASE 2: NETWORK SCANNING")
        print(f"{'='*60}")
        
        # Validate network reachability first
        reachable_networks = validate_network_reachability(self.discovered_networks)
        
        if not reachable_networks:
            print("Warning: No networks appear to be reachable for scanning")
            return False
        
        # Perform ping sweep
        self.ping_results = ping_sweep_all_networks(reachable_networks)
        
        return True

    def scan_devices(self) -> bool:
        """Scan responding hosts with NMAP to identify device types"""
        # Collect IP addresses that are associated with MAC addresses on non-uplink ports
        target_ips = set()

        for switch_name, switch_data in self.switch_data.items():
            if not switch_data['connected']:
                continue

            # Get uplink ports
            uplink_ports = set(switch_data['cdp_neighbors'].keys())

            # Create MAC to ARP mapping
            mac_to_arp = {}
            for arp_entry in switch_data['arp_table']:
                mac_to_arp[arp_entry['mac']] = arp_entry

            # Check MAC table entries
            for mac_entry in switch_data['mac_table']:
                port_name = mac_entry['ports']
                mac_address = mac_entry['mac']

                # Skip if this MAC is learned from an uplink port
                if port_name in uplink_ports:
                    continue

                # Get IP address for this MAC
                arp_info = mac_to_arp.get(mac_address, {})
                ip_address = arp_info.get('ip', '')

                if ip_address:
                    target_ips.add(ip_address)

        if not target_ips:
            print("No target IP addresses found for scanning")
            return False

        print(f"\n{'='*60}")
        print("PHASE 3: DEVICE IDENTIFICATION")
        print(f"{'='*60}")
        print(f"Found {len(target_ips)} IP addresses associated with non-uplink ports")
        print("Target IPs:", sorted(target_ips))

        # Scan only the targeted devices with NMAP
        self.device_info = scan_responding_hosts(list(target_ips), max_workers=10)

        # Show summary
        get_device_summary(self.device_info)

        # Save device info
        save_device_info(self.device_info, 'device_info.json')

        return True

    def generate_reports(self) -> bool:
        """Generate comprehensive reports"""
        print(f"\n{'='*60}")
        print("PHASE 4: REPORT GENERATION")
        print(f"{'='*60}")

        try:
            # Generate comprehensive report
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'switches': self.switch_data,
                'networks': list(self.discovered_networks),
                'ping_results': self.ping_results,
                'device_info': self.device_info
            }
            
            # Save raw data as JSON
            with open('network_discovery_data.json', 'w') as f:
                # Convert sets to lists for JSON serialization
                json_data = {}
                for key, value in report_data.items():
                    if key == 'switches':
                        json_data[key] = {}
                        for switch_name, switch_data in value.items():
                            json_data[key][switch_name] = dict(switch_data)
                            if 'networks' in json_data[key][switch_name]:
                                json_data[key][switch_name]['networks'] = list(switch_data['networks'])
                    else:
                        json_data[key] = value
                
                json.dump(json_data, f, indent=2, default=str)
            
            print("Raw data saved to: network_discovery_data.json")
            
            # Generate human-readable reports
            generate_comprehensive_report(report_data)
            
            return True
            
        except Exception as e:
            print(f"Error generating reports: {e}")
            return False
    
    def run_discovery(self) -> bool:
        """Run the complete network discovery process"""
        print("="*60)
        print("NETWORK DISCOVERY AND ANALYSIS")
        print("="*60)
        print(f"Started at: {datetime.now()}")
        print("\nIMPORTANT: This script is READ-ONLY and will not modify any switch configurations.")
        
        # Load switches
        if not self.load_switches():
            return False
        
        # Discover networks
        if not self.discover_networks():
            print("Failed to discover networks")
            return False
        
        # Perform network scan
        self.perform_network_scan()

        # Scan devices to identify what they are
        self.scan_devices()

        # Generate reports
        if not self.generate_reports():
            print("Failed to generate reports")
            return False
        
        print(f"\nDiscovery completed at: {datetime.now()}")
        print("Check the generated report files for detailed analysis.")
        
        return True


def main():
    """Main function"""
    if len(sys.argv) > 1:
        switch_file = sys.argv[1]
    else:
        switch_file = "switch_list.txt"
    
    discovery = NetworkDiscovery(switch_file)
    
    try:
        success = discovery.run_discovery()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nDiscovery interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
